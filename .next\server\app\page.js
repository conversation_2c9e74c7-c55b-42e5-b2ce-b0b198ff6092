/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5Ccoding%20projects%5CNext%20js%5Crichport%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Ccoding%20projects%5CNext%20js%5Crichport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5Ccoding%20projects%5CNext%20js%5Crichport%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Ccoding%20projects%5CNext%20js%5Crichport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"E:\\\\coding projects\\\\Next js\\\\richport\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"E:\\\\coding projects\\\\Next js\\\\richport\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\coding projects\\\\Next js\\\\richport\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5Ccoding%20projects%5CNext%20js%5Crichport%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Ccoding%20projects%5CNext%20js%5Crichport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CSmoothScroller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Cfonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Cfonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22Newsreader%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-newsreader%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22200%5C%22%2C%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%5D%2C%5C%22style%5C%22%3A%5B%5C%22normal%5C%22%2C%5C%22italic%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22newsreader%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Cfonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22Fraunces%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-fraunces%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%2C%5C%22style%5C%22%3A%5B%5C%22normal%5C%22%2C%5C%22italic%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22fraunces%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CSmoothScroller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Cfonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Cfonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22Newsreader%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-newsreader%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22200%5C%22%2C%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%5D%2C%5C%22style%5C%22%3A%5B%5C%22normal%5C%22%2C%5C%22italic%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22newsreader%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Cfonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22Fraunces%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-fraunces%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%2C%5C%22style%5C%22%3A%5B%5C%22normal%5C%22%2C%5C%22italic%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22fraunces%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/SmoothScroller.tsx */ \"(ssr)/./components/SmoothScroller.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CSmoothScroller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Cfonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Cfonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22Newsreader%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-newsreader%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22200%5C%22%2C%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%5D%2C%5C%22style%5C%22%3A%5B%5C%22normal%5C%22%2C%5C%22italic%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22newsreader%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Cfonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22Fraunces%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-fraunces%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%2C%5C%22style%5C%22%3A%5B%5C%22normal%5C%22%2C%5C%22italic%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22fraunces%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CAwards.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CCursor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CFeaturedCarousel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CHeaderStrip.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CMasthead.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CPixelPerfect.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CProcessSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CTestimonials.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CTopBar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CWebsiteBanner.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CAwards.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CCursor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CFeaturedCarousel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CHeaderStrip.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CMasthead.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CPixelPerfect.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CProcessSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CTestimonials.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CTopBar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CWebsiteBanner.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Awards.tsx */ \"(ssr)/./components/Awards.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Cursor.tsx */ \"(ssr)/./components/Cursor.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/FeaturedCarousel.tsx */ \"(ssr)/./components/FeaturedCarousel.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Header.tsx */ \"(ssr)/./components/Header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/HeaderStrip.tsx */ \"(ssr)/./components/HeaderStrip.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Hero.tsx */ \"(ssr)/./components/Hero.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Masthead.tsx */ \"(ssr)/./components/Masthead.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/PixelPerfect.tsx */ \"(ssr)/./components/PixelPerfect.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ProcessSection.tsx */ \"(ssr)/./components/ProcessSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Testimonials.tsx */ \"(ssr)/./components/Testimonials.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/TopBar.tsx */ \"(ssr)/./components/TopBar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/WebsiteBanner.tsx */ \"(ssr)/./components/WebsiteBanner.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(ssr)/./node_modules/next/dist/client/image-component.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CAwards.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CCursor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CFeaturedCarousel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CHeaderStrip.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CMasthead.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CPixelPerfect.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CProcessSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CTestimonials.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CTopBar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CWebsiteBanner.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/Awards.tsx":
/*!*******************************!*\
  !*** ./components/Awards.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Awards)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_gsap__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/gsap */ \"(ssr)/./lib/gsap.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst STATS = [\n    {\n        label: \"Site of the day Awards\",\n        value: 9\n    },\n    {\n        label: \"Site of the month Winners\",\n        value: 1\n    },\n    {\n        label: \"FWA OF THE DAY Awards\",\n        value: 6\n    },\n    {\n        label: \"acclaimed Menti o ns\",\n        value: 8\n    }\n];\nfunction Awards() {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!ref.current) return;\n        const nums = ref.current.querySelectorAll(\"[data-num]\");\n        nums.forEach((el)=>{\n            const end = Number(el.getAttribute(\"data-num\"));\n            _lib_gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.fromTo(el, {\n                innerText: 0\n            }, {\n                innerText: end,\n                duration: 1.2,\n                ease: \"power1.out\",\n                snap: {\n                    innerText: 1\n                },\n                scrollTrigger: {\n                    trigger: el,\n                    start: \"top 80%\"\n                }\n            });\n        });\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"mx-auto max-w-6xl px-4 py-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            ref: ref,\n            className: \"grid sm:grid-cols-2 md:grid-cols-4 gap-8\",\n            children: STATS.map((s, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-5xl font-serif\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                \"data-num\": s.value,\n                                children: s.value\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Awards.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 50\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Awards.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 text-sm text-ink/70\",\n                            children: s.label\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Awards.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, i, true, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Awards.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Awards.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Awards.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Awards.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Cursor.tsx":
/*!*******************************!*\
  !*** ./components/Cursor.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Cursor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_gsap__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/gsap */ \"(ssr)/./lib/gsap.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Cursor() {\n    const dot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const ring = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!dot.current || !ring.current) return;\n        const qx = _lib_gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.quickTo(dot.current, \"x\", {\n            duration: 0.12,\n            ease: \"power3\"\n        });\n        const qy = _lib_gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.quickTo(dot.current, \"y\", {\n            duration: 0.12,\n            ease: \"power3\"\n        });\n        const rx = _lib_gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.quickTo(ring.current, \"x\", {\n            duration: 0.24,\n            ease: \"power3\"\n        });\n        const ry = _lib_gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.quickTo(ring.current, \"y\", {\n            duration: 0.24,\n            ease: \"power3\"\n        });\n        const onMove = (e)=>{\n            qx(e.clientX);\n            qy(e.clientY);\n            rx(e.clientX);\n            ry(e.clientY);\n        };\n        window.addEventListener(\"mousemove\", onMove);\n        return ()=>window.removeEventListener(\"mousemove\", onMove);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: dot,\n                className: \"cursor-dot\"\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Cursor.tsx\",\n                lineNumber: 21,\n                columnNumber: 5\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: ring,\n                className: \"cursor-ring\"\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Cursor.tsx\",\n                lineNumber: 22,\n                columnNumber: 5\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Cursor.tsx\n");

/***/ }),

/***/ "(ssr)/./components/FeaturedCarousel.tsx":
/*!*****************************************!*\
  !*** ./components/FeaturedCarousel.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeaturedCarousel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_gsap__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/gsap */ \"(ssr)/./lib/gsap.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst ITEMS = [\n    {\n        title: \"The Roger Hub\",\n        subtitle: `An immersive web experience showcasing the tennis-inspired 'On' sneakers, a collaboration born out of a partnership with the legendary Roger Federer.`,\n        image: \"https://images.unsplash.com/photo-1542291026-7eec264c27ff?q=80&w=1600&auto=format&fit=crop\",\n        year: \"2023\"\n    },\n    {\n        title: \"WOW Concept\",\n        subtitle: `A concept store experience in Madrid revolutionizing retail with dynamic, interactive shopping.`,\n        image: \"https://images.unsplash.com/photo-1541101767792-f9b2b1c4f127?q=80&w=1600&auto=format&fit=crop\",\n        year: \"2023\"\n    },\n    {\n        title: \"Prada Employees\",\n        subtitle: `An eCommerce outlet gathering previous collection seasons on a minimalist-based design.`,\n        image: \"https://images.unsplash.com/photo-1512436991641-6745cdb1723f?q=80&w=1600&auto=format&fit=crop\",\n        year: \"2022\"\n    },\n    {\n        title: \"The Books of Ye\",\n        subtitle: `Conceptual NFT narrative replacing instances of God with Ye across the five Books of Moses.`,\n        image: \"https://images.unsplash.com/photo-1524995997946-a1c2e315a42f?q=80&w=1600&auto=format&fit=crop\",\n        year: \"2022\"\n    }\n];\nfunction FeaturedCarousel() {\n    const wrap = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const track = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!wrap.current || !track.current) return;\n        const cards = _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.utils.toArray(track.current.querySelectorAll(\".card\"));\n        const totalWidth = ()=>cards.reduce((acc, el)=>acc + el.offsetWidth + 24, 0);\n        _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.set(track.current, {\n            width: totalWidth()\n        });\n        // Draggable (without inertia) for broad compatibility\n        const maxX = ()=>Math.min(0, wrap.current.offsetWidth - totalWidth());\n        let draggable = _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.Draggable.create(track.current, {\n            type: \"x\",\n            bounds: {\n                minX: maxX(),\n                maxX: 0\n            },\n            inertia: false\n        })[0];\n        const onResize = ()=>{\n            _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.set(track.current, {\n                width: totalWidth()\n            });\n            draggable.applyBounds({\n                minX: maxX(),\n                maxX: 0\n            });\n        };\n        window.addEventListener(\"resize\", onResize);\n        return ()=>{\n            window.removeEventListener(\"resize\", onResize);\n            draggable.kill();\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"mx-auto max-w-6xl px-4 pb-20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-end justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"helper-tag\",\n                                children: \"A Featured selection\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\FeaturedCarousel.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-ink/70\",\n                                children: \"the latest work – of the last years.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\FeaturedCarousel.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\FeaturedCarousel.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-ink/60\",\n                        children: \"Tip! Drag sideways to navigate\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\FeaturedCarousel.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\FeaturedCarousel.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: wrap,\n                className: \"overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    ref: track,\n                    className: \"flex gap-6 will-change-transform select-none cursor-grab active:cursor-grabbing\",\n                    children: ITEMS.map((it, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                            className: \"card relative w-[320px] sm:w-[420px] shrink-0 bg-white/50 rounded-2xl shadow-soft overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative h-[220px] sm:h-[280px]\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        src: it.image,\n                                        alt: it.title,\n                                        fill: true,\n                                        className: \"object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\FeaturedCarousel.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\FeaturedCarousel.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs uppercase tracking-widest text-ink/60\",\n                                            children: [\n                                                \"New \\xb7 \",\n                                                it.year\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\FeaturedCarousel.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"mt-1 font-serif text-2xl\",\n                                            children: it.title\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\FeaturedCarousel.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-sm text-ink/70\",\n                                            children: it.subtitle\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\FeaturedCarousel.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\FeaturedCarousel.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\FeaturedCarousel.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\FeaturedCarousel.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\FeaturedCarousel.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\FeaturedCarousel.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/FeaturedCarousel.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_gsap__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/gsap */ \"(ssr)/./lib/gsap.ts\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst NavLink = ({ href, children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        href: href,\n        className: \"magnet group inline-flex items-center gap-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"tracking-loose2\",\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n                lineNumber: 10,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"block h-px w-6 bg-ink/40 transition-all group-hover:w-10\"\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n                lineNumber: 11,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined);\nfunction Header() {\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // subtle reveal\n        _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.from(ref.current.querySelectorAll(\"[data-reveal] > *\"), {\n            opacity: 0,\n            y: 14,\n            stagger: 0.06,\n            duration: 0.5,\n            ease: \"power2.out\"\n        });\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-40 bg-paper supports-[backdrop-filter]:bg-paper/50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mx-auto max-w-6xl px-4 py-4 flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    href: \"/\",\n                    className: \"doodle-wrap flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm\",\n                            children: \"Kaitare, Richard\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"doodle-stroke w-10 h-8\",\n                            viewBox: \"0 0 100 80\",\n                            \"aria-hidden\": true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M10 40 Q 50 5, 90 40 Q 50 75, 10 40 z\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: (0,clsx__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(\"site-nav\", \"flex items-center gap-6 text-sm\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                            href: \"/\",\n                            children: \"Home\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                            href: \"/work\",\n                            children: \"Work\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                            href: \"/about\",\n                            children: \"About\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./components/HeaderStrip.tsx":
/*!************************************!*\
  !*** ./components/HeaderStrip.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HeaderStrip)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_gsap__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/gsap */ \"(ssr)/./lib/gsap.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction NewBadge() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"badge-new\",\n        children: \"NEW\"\n    }, void 0, false, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 7,\n        columnNumber: 10\n    }, this);\n}\nfunction MiniEditorialCard({ title, image, children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n        className: \"px-4 md:px-6 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative aspect-[16/9] rounded-md overflow-hidden mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    src: image,\n                    alt: title,\n                    fill: true,\n                    className: \"object-cover\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                className: \"font-display text-[22px] leading-none tracking-[.02em] uppercase flex items-center gap-2\",\n                children: [\n                    title,\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NewBadge, {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-[15px] leading-6 text-ink/80\",\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\nfunction CenterCopy() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-4 md:px-6 py-8 text-center md:text-left\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-display text-5xl md:text-[56px] leading-[0.95]\",\n                children: \"ALL WORK!\"\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-display text-[26px] md:text-[30px] leading-[1.05] mt-3\",\n                children: [\n                    \"A Featured selection\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 29\n                    }, this),\n                    \" the latest work —\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 52\n                    }, this),\n                    \" of the last years.\"\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 text-[12px] uppercase tracking-wider text-ink/70\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Tip!\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    \" Drag sideways to navigate\"\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\nfunction HeaderStrip() {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!ref.current) return;\n        _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.from(ref.current.querySelectorAll(\"[data-reveal] > *\"), {\n            opacity: 0,\n            y: 14,\n            stagger: 0.06,\n            duration: 0.5,\n            ease: \"power2.out\"\n        });\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: ref,\n        className: \"border-y border-ink/15\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mx-auto max-w-6xl grid grid-cols-1 md:grid-cols-3 divide-y md:divide-y-0 md:divide-x divide-ink/20\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    \"data-reveal\": true,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MiniEditorialCard, {\n                        title: \"AvroKO\",\n                        image: \"/hero/thumbnail-small.jpeg\",\n                        children: \"AvroKO is an award-winning global design firm, established itself as a global leader in interior architecture for hospitality, restaurant and bars.\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-paper/60\",\n                    \"data-reveal\": true,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CenterCopy, {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    \"data-reveal\": true,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MiniEditorialCard, {\n                        title: \"The Roger Hub\",\n                        image: \"/hero/thumbnail-small-3.jpeg\",\n                        children: \"The Roger Hub is an immersive web experience showcasing the tennis-inspired ‘On’ sneakers, a collaboration born out of a partnership with the legendary Roger Federer.\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/HeaderStrip.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Hero.tsx":
/*!*****************************!*\
  !*** ./components/Hero.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Hero)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_gsap__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/gsap */ \"(ssr)/./lib/gsap.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction splitToSpans(el) {\n    const text = el.innerText;\n    el.innerHTML = \"\";\n    Array.from(text).forEach((ch)=>{\n        const s = document.createElement(\"span\");\n        s.textContent = ch;\n        el.appendChild(s);\n    });\n}\nfunction Hero() {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!ref.current) return;\n        const title = ref.current.querySelector(\".h-split\");\n        if (title) splitToSpans(title);\n        const tl = _lib_gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.timeline();\n        tl.from(ref.current.querySelectorAll(\".hero-kicker, .h-split span, .hero-bio\"), {\n            opacity: 0,\n            y: 20,\n            stagger: 0.02,\n            ease: \"power2.out\",\n            duration: 0.8\n        });\n        _lib_gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.from(\".hero-scrolly\", {\n            opacity: 0,\n            y: 10,\n            scrollTrigger: {\n                trigger: ref.current,\n                start: \"bottom bottom\"\n            }\n        });\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: ref,\n        className: \"relative mx-auto max-w-6xl px-4 pt-16 pb-20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"helper-tag mb-3\",\n                children: \"# Miranda\"\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Hero.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-6xl md:text-7xl lg:text-8xl font-serif leading-[0.95] tracking-[-0.02em] h-split\",\n                children: [\n                    \"Intera c tive\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Hero.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 22\n                    }, this),\n                    \" Artist!\"\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Hero.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"hero-bio mt-6 max-w-3xl text-base/7 md:text-lg/8 text-ink/80\",\n                children: \"As a multidisciplinary freelancer, I'm passionate about creating iconic digital experiences through motion, typography, and creative coding for companies and agencies around the world.\"\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Hero.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 flex items-center gap-6 text-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"mailto:<EMAIL>\",\n                        className: \"underline decoration-accent underline-offset-4\",\n                        children: \"Email Me\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Hero.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"— Based in Amsterdam, NL.\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Hero.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Hero.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hero-scrolly mt-16 text-sm text-ink/60\",\n                children: \"Drag sideways to navigate ↓\"\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Hero.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Hero.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Hero.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Masthead.tsx":
/*!*********************************!*\
  !*** ./components/Masthead.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Masthead)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_StampBadge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/StampBadge */ \"(ssr)/./components/StampBadge.tsx\");\n/* harmony import */ var _lib_gsap__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/gsap */ \"(ssr)/./lib/gsap.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Masthead() {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (!ref.current) return;\n        _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.from(ref.current.querySelectorAll(\"[data-reveal]\"), {\n            opacity: 0,\n            y: 18,\n            stagger: .08,\n            duration: .6,\n            ease: \"power3.out\"\n        });\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: ref,\n        className: \"mx-auto max-w-6xl px-4 py-10 grid md:grid-cols-12 gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:col-span-4 boxed p-4\",\n                \"data-reveal\": true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs uppercase tracking-widest text-ink/60 mb-2\",\n                        children: \"Hero 002\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Masthead.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative aspect-[4/3] overflow-hidden rounded-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            src: \"https://images.unsplash.com/photo-1542291026-7eec264c27ff?q=80&w=1600&auto=format&fit=crop\",\n                            alt: \"\",\n                            fill: true,\n                            className: \"object-cover\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Masthead.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Masthead.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-sm text-ink/70\",\n                        children: \"Awarded 2023 — editorial grids & motion.\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Masthead.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Masthead.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:col-span-4 boxed p-4\",\n                \"data-reveal\": true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs uppercase tracking-widest text-ink/60 mb-2\",\n                        children: \"All Work!\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Masthead.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-base\",\n                        children: \"A Featured selection — the latest work of the last years.\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Masthead.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Masthead.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:col-span-4 boxed p-4\",\n                \"data-reveal\": true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs uppercase tracking-widest text-ink/60 mb-2\",\n                        children: \"The Roger Hub\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Masthead.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative aspect-[4/3] overflow-hidden rounded-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            src: \"https://images.unsplash.com/photo-1512436991641-6745cdb1723f?q=80&w=1600&auto=format&fit=crop\",\n                            alt: \"\",\n                            fill: true,\n                            className: \"object-cover\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Masthead.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Masthead.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Masthead.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:col-span-12 boxed p-4 sm:p-6\",\n                \"data-reveal\": true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"font-display text-[16vw] leading-none tracking-[-.03em] text-ink/90 text-center md:text-left\",\n                    children: \"MIRANDA\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Masthead.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Masthead.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:col-span-6 boxed p-4 md:p-6\",\n                \"data-reveal\": true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-display text-6xl md:text-7xl leading-[.9]\",\n                        children: [\n                            \"INTERACTIVE\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Masthead.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 83\n                            }, this),\n                            \"ARTIST!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Masthead.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"dropcap mt-4 text-ink/80 max-w-prose\",\n                        children: \"As a multidisciplinary freelancer, I'm passionate about creating iconic digital experiences through motion, typography, and creative coding for companies and agencies around the world.\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Masthead.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Masthead.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:col-span-6 boxed p-2 flex items-center justify-center\",\n                \"data-reveal\": true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-full aspect-square rounded-md overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        src: \"https://images.unsplash.com/photo-1524504388940-b1c1722653e1?q=80&w=1600&auto=format&fit=crop\",\n                        alt: \"\",\n                        fill: true,\n                        className: \"object-cover\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Masthead.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Masthead.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Masthead.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:col-span-8 boxed p-4 md:p-6\",\n                \"data-reveal\": true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"font-display text-7xl md:text-8xl\",\n                    children: \"WEBSITE\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Masthead.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Masthead.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:col-span-4 flex items-center justify-center\",\n                \"data-reveal\": true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StampBadge__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Masthead.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Masthead.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Masthead.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Masthead.tsx\n");

/***/ }),

/***/ "(ssr)/./components/PixelPerfect.tsx":
/*!*************************************!*\
  !*** ./components/PixelPerfect.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PixelPerfect)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_gsap__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/gsap */ \"(ssr)/./lib/gsap.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction PixelPerfect() {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!ref.current) return;\n        _lib_gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.from(ref.current.querySelectorAll(\"[data-reveal]\"), {\n            opacity: 0,\n            y: 18,\n            stagger: .06,\n            duration: .6\n        });\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: ref,\n        className: \"mx-auto max-w-6xl px-4 py-16 grid md:grid-cols-12 gap-6 items-start\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:col-span-7 boxed p-4 md:p-6\",\n                \"data-reveal\": true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"font-display text-7xl md:text-8xl leading-[.88]\",\n                    children: [\n                        \"THE\",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\PixelPerfect.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 14\n                        }, this),\n                        \"PIXEL\",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\PixelPerfect.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 24\n                        }, this),\n                        \"PERFECT\",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\PixelPerfect.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 36\n                        }, this),\n                        \"ARTISAN\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\PixelPerfect.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\PixelPerfect.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:col-span-5 boxed p-6\",\n                \"data-reveal\": true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-ink/80\",\n                        children: \"Over the past 5+ years, I've teamed up with high-profile clients and partners globally, earning mentions & awards.\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\PixelPerfect.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-display text-3xl\",\n                                children: \"AWWWARDS\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\PixelPerfect.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-ink/70\",\n                                children: \"Communication Arts, Site Inspire, Behance, Codrops and many others.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\PixelPerfect.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\PixelPerfect.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\PixelPerfect.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\PixelPerfect.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/PixelPerfect.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ProcessSection.tsx":
/*!***************************************!*\
  !*** ./components/ProcessSection.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProcessSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_gsap__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/gsap */ \"(ssr)/./lib/gsap.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ProcessSection() {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!ref.current) return;\n        const ctx = _lib_gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.context(()=>{\n            _lib_gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.from(\".process h2\", {\n                yPercent: 20,\n                opacity: 0,\n                stagger: 0.2,\n                scrollTrigger: {\n                    trigger: ref.current,\n                    start: \"top 80%\"\n                }\n            });\n            _lib_gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.from(\".process p\", {\n                y: 20,\n                opacity: 0,\n                stagger: 0.1,\n                scrollTrigger: {\n                    trigger: ref.current,\n                    start: \"top 75%\"\n                }\n            });\n        }, ref);\n        return ()=>ctx.revert();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: ref,\n        className: \"process mx-auto max-w-6xl px-4 py-24 grid md:grid-cols-2 gap-10 items-start\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"font-serif text-5xl leading-tight\",\n                    children: [\n                        \"Think, C reate\",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\ProcessSection.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 73\n                        }, this),\n                        \" Deliver\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\ProcessSection.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\ProcessSection.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-ink/80\",\n                        children: \"A strong project is created by deep collaboration. I design, develop, and deliver websites that drive results and win awards. Like an artisan, I like to start from raw matter and give life to an iconic product that makes your brand stand out, starting from a Visual Strategy that guides the vision to reality.\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\ProcessSection.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        className: \"inline-block mt-6 underline decoration-accent underline-offset-4\",\n                        href: \"/work\",\n                        children: \"All W o rk →\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\ProcessSection.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\ProcessSection.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\ProcessSection.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ProcessSection.tsx\n");

/***/ }),

/***/ "(ssr)/./components/SmoothScroller.tsx":
/*!***************************************!*\
  !*** ./components/SmoothScroller.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SmoothScroller)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lenis__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lenis */ \"(ssr)/./node_modules/lenis/dist/lenis.mjs\");\n/* harmony import */ var _lib_gsap__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/gsap */ \"(ssr)/./lib/gsap.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction SmoothScroller() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const lenis = new lenis__WEBPACK_IMPORTED_MODULE_2__[\"default\"]({\n            smoothWheel: true,\n            smoothTouch: false\n        });\n        function raf(time) {\n            lenis.raf(time);\n            requestAnimationFrame(raf);\n        }\n        requestAnimationFrame(raf);\n        // Sync Lenis with ScrollTrigger\n        lenis.on(\"scroll\", _lib_gsap__WEBPACK_IMPORTED_MODULE_1__.ScrollTrigger.update);\n        return ()=>{\n            lenis?.destroy();\n        };\n    }, []);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL1Ntb290aFNjcm9sbGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs2REFDaUM7QUFDUjtBQUNpQjtBQUUzQixTQUFTRztJQUN0QkgsZ0RBQVNBLENBQUM7UUFDUixNQUFNSSxRQUFRLElBQUlILDZDQUFLQSxDQUFDO1lBQUVJLGFBQWE7WUFBTUMsYUFBYTtRQUFNO1FBQ2hFLFNBQVNDLElBQUlDLElBQVk7WUFDdkJKLE1BQU1HLEdBQUcsQ0FBQ0M7WUFDVkMsc0JBQXNCRjtRQUN4QjtRQUNBRSxzQkFBc0JGO1FBRXRCLGdDQUFnQztRQUNoQ0gsTUFBTU0sRUFBRSxDQUFDLFVBQVVSLG9EQUFhQSxDQUFDUyxNQUFNO1FBQ3ZDLE9BQU87WUFBU1AsT0FBZVE7UUFBVTtJQUMzQyxHQUFHLEVBQUU7SUFDTCxPQUFPO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uaWNjb2xvLXBhcGVyLXBvcnRmb2xpby1jbG9uZS8uL2NvbXBvbmVudHMvU21vb3RoU2Nyb2xsZXIudHN4P2IyZDEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5pbXBvcnQgeyB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCBMZW5pcyBmcm9tICdsZW5pcydcbmltcG9ydCB7IFNjcm9sbFRyaWdnZXIgfSBmcm9tICdAL2xpYi9nc2FwJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTbW9vdGhTY3JvbGxlcigpIHtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBsZW5pcyA9IG5ldyBMZW5pcyh7IHNtb290aFdoZWVsOiB0cnVlLCBzbW9vdGhUb3VjaDogZmFsc2UgfSlcbiAgICBmdW5jdGlvbiByYWYodGltZTogbnVtYmVyKSB7XG4gICAgICBsZW5pcy5yYWYodGltZSlcbiAgICAgIHJlcXVlc3RBbmltYXRpb25GcmFtZShyYWYpXG4gICAgfVxuICAgIHJlcXVlc3RBbmltYXRpb25GcmFtZShyYWYpXG5cbiAgICAvLyBTeW5jIExlbmlzIHdpdGggU2Nyb2xsVHJpZ2dlclxuICAgIGxlbmlzLm9uKCdzY3JvbGwnLCBTY3JvbGxUcmlnZ2VyLnVwZGF0ZSlcbiAgICByZXR1cm4gKCkgPT4geyAobGVuaXMgYXMgYW55KT8uZGVzdHJveSgpIH1cbiAgfSwgW10pXG4gIHJldHVybiBudWxsXG59XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwiTGVuaXMiLCJTY3JvbGxUcmlnZ2VyIiwiU21vb3RoU2Nyb2xsZXIiLCJsZW5pcyIsInNtb290aFdoZWVsIiwic21vb3RoVG91Y2giLCJyYWYiLCJ0aW1lIiwicmVxdWVzdEFuaW1hdGlvbkZyYW1lIiwib24iLCJ1cGRhdGUiLCJkZXN0cm95Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/SmoothScroller.tsx\n");

/***/ }),

/***/ "(ssr)/./components/StampBadge.tsx":
/*!***********************************!*\
  !*** ./components/StampBadge.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StampBadge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction StampBadge() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"stamp p-3 bg-paper\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-20 h-14 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    viewBox: \"0 0 120 60\",\n                    className: \"w-full h-full\",\n                    \"aria-hidden\": true,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M5 55 Q 60 5, 115 55\",\n                            fill: \"none\",\n                            stroke: \"var(--accent)\",\n                            strokeWidth: \"4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\StampBadge.tsx\",\n                            lineNumber: 6,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M25 30 L 95 30\",\n                            stroke: \"var(--accent)\",\n                            strokeWidth: \"2\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\StampBadge.tsx\",\n                            lineNumber: 7,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\StampBadge.tsx\",\n                    lineNumber: 5,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\StampBadge.tsx\",\n                lineNumber: 4,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-[10px] text-ink/70 mt-1\",\n                children: \"NM — AMS\"\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\StampBadge.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\StampBadge.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL1N0YW1wQmFkZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBZSxTQUFTQTtJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDQztvQkFBSUMsU0FBUTtvQkFBYUYsV0FBVTtvQkFBZ0JHLGFBQVc7O3NDQUM3RCw4REFBQ0M7NEJBQUtDLEdBQUU7NEJBQXVCQyxNQUFLOzRCQUFPQyxRQUFPOzRCQUFnQkMsYUFBWTs7Ozs7O3NDQUM5RSw4REFBQ0o7NEJBQUtDLEdBQUU7NEJBQWlCRSxRQUFPOzRCQUFnQkMsYUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBR2hFLDhEQUFDVDtnQkFBSUMsV0FBVTswQkFBK0I7Ozs7Ozs7Ozs7OztBQUdwRCIsInNvdXJjZXMiOlsid2VicGFjazovL25pY2NvbG8tcGFwZXItcG9ydGZvbGlvLWNsb25lLy4vY29tcG9uZW50cy9TdGFtcEJhZGdlLnRzeD8xOWJiIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFN0YW1wQmFkZ2UoKXtcclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzdGFtcCBwLTMgYmctcGFwZXJcIj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIwIGgtMTQgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cclxuICAgICAgICA8c3ZnIHZpZXdCb3g9XCIwIDAgMTIwIDYwXCIgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbFwiIGFyaWEtaGlkZGVuPlxyXG4gICAgICAgICAgPHBhdGggZD1cIk01IDU1IFEgNjAgNSwgMTE1IDU1XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJ2YXIoLS1hY2NlbnQpXCIgc3Ryb2tlV2lkdGg9XCI0XCIgLz5cclxuICAgICAgICAgIDxwYXRoIGQ9XCJNMjUgMzAgTCA5NSAzMFwiIHN0cm9rZT1cInZhcigtLWFjY2VudClcIiBzdHJva2VXaWR0aD1cIjJcIiAvPlxyXG4gICAgICAgIDwvc3ZnPlxyXG4gICAgICA8L2Rpdj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LVsxMHB4XSB0ZXh0LWluay83MCBtdC0xXCI+Tk0g4oCUIEFNUzwvZGl2PlxyXG4gICAgPC9kaXY+XHJcbiAgKVxyXG59XHJcbiJdLCJuYW1lcyI6WyJTdGFtcEJhZGdlIiwiZGl2IiwiY2xhc3NOYW1lIiwic3ZnIiwidmlld0JveCIsImFyaWEtaGlkZGVuIiwicGF0aCIsImQiLCJmaWxsIiwic3Ryb2tlIiwic3Ryb2tlV2lkdGgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/StampBadge.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Testimonials.tsx":
/*!*************************************!*\
  !*** ./components/Testimonials.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Testimonials)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_gsap__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/gsap */ \"(ssr)/./lib/gsap.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst QUOTES = [\n    {\n        quote: \"“Blurring the line between design & dev, this work has an unmatched eye for detail and precise execution.”\",\n        name: \"Sam Day\",\n        role: \"Creative Director & Designer\"\n    },\n    {\n        quote: \"“One of the leaders in today’s digital design scene.”\",\n        name: \"Sofia Papadopoulou\",\n        role: \"Designer & Art Director\"\n    },\n    {\n        quote: \"“High-skilled designer who creates novel experiences with ease and craft.”\",\n        name: \"Bruno Arizio\",\n        role: \"Creative Director\"\n    }\n];\nfunction Testimonials() {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!ref.current) return;\n        _lib_gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.from(ref.current.querySelectorAll(\"blockquote\"), {\n            opacity: 0,\n            y: 10,\n            stagger: 0.1,\n            duration: 0.6,\n            scrollTrigger: {\n                trigger: ref.current,\n                start: \"top 80%\"\n            }\n        });\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"mx-auto max-w-6xl px-4 py-16\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid md:grid-cols-3 gap-8\",\n            children: QUOTES.map((q, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                    className: \"dotted bg-white/50 rounded-2xl p-6 shadow-soft dotted \",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-ink/90\",\n                            children: q.quote\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Testimonials.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                            className: \"mt-4 text-sm text-ink/70\",\n                            children: [\n                                q.name,\n                                \" — \",\n                                q.role\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Testimonials.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, i, true, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Testimonials.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Testimonials.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Testimonials.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL1Rlc3RpbW9uaWFscy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUN5QztBQUNPO0FBRWhELE1BQU1HLFNBQVM7SUFDYjtRQUNFQyxPQUFPO1FBQ1BDLE1BQU07UUFDTkMsTUFBTTtJQUNSO0lBQ0E7UUFDRUYsT0FBTztRQUNQQyxNQUFNO1FBQ05DLE1BQU07SUFDUjtJQUNBO1FBQ0VGLE9BQU87UUFDUEMsTUFBTTtRQUNOQyxNQUFNO0lBQ1I7Q0FDRDtBQUVjLFNBQVNDO0lBQ3RCLE1BQU1DLE1BQU1QLDZDQUFNQSxDQUFpQjtJQUNuQ0QsZ0RBQVNBLENBQUM7UUFDUixJQUFJLENBQUNRLElBQUlDLE9BQU8sRUFBRTtRQUNsQlAsMkNBQUlBLENBQUNRLElBQUksQ0FBQ0YsSUFBSUMsT0FBTyxDQUFDRSxnQkFBZ0IsQ0FBQyxlQUFlO1lBQ3BEQyxTQUFTO1lBQ1RDLEdBQUc7WUFDSEMsU0FBUztZQUNUQyxVQUFVO1lBQ1ZDLGVBQWU7Z0JBQUVDLFNBQVNULElBQUlDLE9BQU87Z0JBQUVTLE9BQU87WUFBVTtRQUMxRDtJQUNGLEdBQUcsRUFBRTtJQUVMLHFCQUNFLDhEQUFDQztRQUFRQyxXQUFVO2tCQUNqQiw0RUFBQ0M7WUFBSUQsV0FBVTtzQkFDWmpCLE9BQU9tQixHQUFHLENBQUMsQ0FBQ0MsR0FBR0Msa0JBQ2QsOERBQUNDO29CQUFtQkwsV0FBVTs7c0NBQzVCLDhEQUFDTTs0QkFBRU4sV0FBVTtzQ0FBZUcsRUFBRW5CLEtBQUs7Ozs7OztzQ0FDbkMsOERBQUN1Qjs0QkFBT1AsV0FBVTs7Z0NBQTRCRyxFQUFFbEIsSUFBSTtnQ0FBQztnQ0FBSWtCLEVBQUVqQixJQUFJOzs7Ozs7OzttQkFGaERrQjs7Ozs7Ozs7Ozs7Ozs7O0FBUTNCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmljY29sby1wYXBlci1wb3J0Zm9saW8tY2xvbmUvLi9jb21wb25lbnRzL1Rlc3RpbW9uaWFscy50c3g/MzRlOSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlUmVmIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBnc2FwLCBTY3JvbGxUcmlnZ2VyIH0gZnJvbSAnQC9saWIvZ3NhcCdcblxuY29uc3QgUVVPVEVTID0gW1xuICB7XG4gICAgcXVvdGU6ICfigJxCbHVycmluZyB0aGUgbGluZSBiZXR3ZWVuIGRlc2lnbiAmIGRldiwgdGhpcyB3b3JrIGhhcyBhbiB1bm1hdGNoZWQgZXllIGZvciBkZXRhaWwgYW5kIHByZWNpc2UgZXhlY3V0aW9uLuKAnScsXG4gICAgbmFtZTogJ1NhbSBEYXknLFxuICAgIHJvbGU6ICdDcmVhdGl2ZSBEaXJlY3RvciAmIERlc2lnbmVyJ1xuICB9LFxuICB7XG4gICAgcXVvdGU6ICfigJxPbmUgb2YgdGhlIGxlYWRlcnMgaW4gdG9kYXnigJlzIGRpZ2l0YWwgZGVzaWduIHNjZW5lLuKAnScsXG4gICAgbmFtZTogJ1NvZmlhIFBhcGFkb3BvdWxvdScsXG4gICAgcm9sZTogJ0Rlc2lnbmVyICYgQXJ0IERpcmVjdG9yJ1xuICB9LFxuICB7XG4gICAgcXVvdGU6ICfigJxIaWdoLXNraWxsZWQgZGVzaWduZXIgd2hvIGNyZWF0ZXMgbm92ZWwgZXhwZXJpZW5jZXMgd2l0aCBlYXNlIGFuZCBjcmFmdC7igJ0nLFxuICAgIG5hbWU6ICdCcnVubyBBcml6aW8nLFxuICAgIHJvbGU6ICdDcmVhdGl2ZSBEaXJlY3RvcidcbiAgfSxcbl1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gVGVzdGltb25pYWxzKCkge1xuICBjb25zdCByZWYgPSB1c2VSZWY8SFRNTERpdkVsZW1lbnQ+KG51bGwpXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFyZWYuY3VycmVudCkgcmV0dXJuXG4gICAgZ3NhcC5mcm9tKHJlZi5jdXJyZW50LnF1ZXJ5U2VsZWN0b3JBbGwoJ2Jsb2NrcXVvdGUnKSwge1xuICAgICAgb3BhY2l0eTogMCxcbiAgICAgIHk6IDEwLFxuICAgICAgc3RhZ2dlcjogMC4xLFxuICAgICAgZHVyYXRpb246IDAuNixcbiAgICAgIHNjcm9sbFRyaWdnZXI6IHsgdHJpZ2dlcjogcmVmLmN1cnJlbnQsIHN0YXJ0OiAndG9wIDgwJScgfVxuICAgIH0pXG4gIH0sIFtdKVxuXG4gIHJldHVybiAoXG4gICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwibXgtYXV0byBtYXgtdy02eGwgcHgtNCBweS0xNlwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIG1kOmdyaWQtY29scy0zIGdhcC04XCI+XG4gICAgICAgIHtRVU9URVMubWFwKChxLCBpKSA9PiAoXG4gICAgICAgICAgPGJsb2NrcXVvdGUga2V5PXtpfSBjbGFzc05hbWU9XCJkb3R0ZWQgYmctd2hpdGUvNTAgcm91bmRlZC0yeGwgcC02IHNoYWRvdy1zb2Z0IGRvdHRlZCBcIj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtaW5rLzkwXCI+e3EucXVvdGV9PC9wPlxuICAgICAgICAgICAgPGZvb3RlciBjbGFzc05hbWU9XCJtdC00IHRleHQtc20gdGV4dC1pbmsvNzBcIj57cS5uYW1lfSDigJQge3Eucm9sZX08L2Zvb3Rlcj5cbiAgICAgICAgICA8L2Jsb2NrcXVvdGU+XG4gICAgICAgICkpfVxuICAgICAgPC9kaXY+XG4gICAgPC9zZWN0aW9uPlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlUmVmIiwiZ3NhcCIsIlFVT1RFUyIsInF1b3RlIiwibmFtZSIsInJvbGUiLCJUZXN0aW1vbmlhbHMiLCJyZWYiLCJjdXJyZW50IiwiZnJvbSIsInF1ZXJ5U2VsZWN0b3JBbGwiLCJvcGFjaXR5IiwieSIsInN0YWdnZXIiLCJkdXJhdGlvbiIsInNjcm9sbFRyaWdnZXIiLCJ0cmlnZ2VyIiwic3RhcnQiLCJzZWN0aW9uIiwiY2xhc3NOYW1lIiwiZGl2IiwibWFwIiwicSIsImkiLCJibG9ja3F1b3RlIiwicCIsImZvb3RlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/Testimonials.tsx\n");

/***/ }),

/***/ "(ssr)/./components/TopBar.tsx":
/*!*******************************!*\
  !*** ./components/TopBar.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TopBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_gsap__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/gsap */ \"(ssr)/./lib/gsap.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction TopBar() {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        _lib_gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.from(\".topbar [data-fade]\", {\n            y: -8,\n            opacity: 0,\n            stagger: 0.05,\n            duration: 0.45,\n            ease: \"power2.out\"\n        });\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"topbar border-b border-ink/15 bg-paper\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mx-auto max-w-6xl px-4 py-3 grid grid-cols-3 items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs md:text-[11px]\",\n                    \"data-fade\": true,\n                    children: \"Richard, Kaitare\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\TopBar.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"justify-self-center font-display text-[14px]\",\n                    \"data-fade\": true,\n                    children: \"The Paper Portfolio\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\TopBar.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"justify-self-end hamburger\",\n                    \"aria-label\": \"Open menu\",\n                    \"data-fade\": true,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\TopBar.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\TopBar.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 19\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\TopBar.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 27\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\TopBar.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\TopBar.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\TopBar.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/TopBar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/WebsiteBanner.tsx":
/*!**************************************!*\
  !*** ./components/WebsiteBanner.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WebsiteBanner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_gsap__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/gsap */ \"(ssr)/./lib/gsap.ts\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction WebsiteBanner() {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!ref.current) return;\n        _lib_gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.from(ref.current.querySelectorAll(\"[data-reveal]\"), {\n            opacity: 0,\n            y: 16,\n            stagger: .06,\n            duration: .55,\n            ease: \"power3.out\"\n        });\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: ref,\n        className: \"mx-auto max-w-6xl px-4 grid md:grid-cols-12 gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:col-span-4 boxed p-4\",\n                \"data-reveal\": true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-display text-3xl\",\n                        children: \"Upcoming Next\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\WebsiteBanner.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm mt-2 text-ink/70\",\n                        children: \"Fresh entry — selected work from the latest releases.\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\WebsiteBanner.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\WebsiteBanner.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:col-span-4 boxed p-2\",\n                \"data-reveal\": true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative aspect-[16/9] rounded-md overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        src: \"https://images.unsplash.com/photo-1682687220742-aba13b6e50ba?q=80&w=1600&auto=format&fit=crop\",\n                        alt: \"\",\n                        fill: true,\n                        className: \"object-cover\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\WebsiteBanner.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\WebsiteBanner.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\WebsiteBanner.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:col-span-4 boxed p-4\",\n                \"data-reveal\": true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-display text-3xl\",\n                        children: [\n                            \"Think, Create\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\WebsiteBanner.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 60\n                            }, this),\n                            \"Deliver\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\WebsiteBanner.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-sm text-ink/80\",\n                        children: \"A strong project is created by deep collaboration. I design, develop, and deliver websites that drive results.\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\WebsiteBanner.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\WebsiteBanner.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\WebsiteBanner.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/WebsiteBanner.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/gsap.ts":
/*!*********************!*\
  !*** ./lib/gsap.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Draggable: () => (/* reexport safe */ gsap_Draggable__WEBPACK_IMPORTED_MODULE_2__.Draggable),\n/* harmony export */   ScrollTrigger: () => (/* reexport safe */ gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_1__.ScrollTrigger),\n/* harmony export */   gsap: () => (/* reexport safe */ gsap__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! gsap */ \"(ssr)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(ssr)/./node_modules/gsap/ScrollTrigger.js\");\n/* harmony import */ var gsap_Draggable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! gsap/Draggable */ \"(ssr)/./node_modules/gsap/Draggable.js\");\n/* __next_internal_client_entry_do_not_use__ gsap,ScrollTrigger,Draggable auto */ \n\n\nif (false) {}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvZ3NhcC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7a0ZBQ3VCO0FBQzJCO0FBQ1I7QUFFMUMsSUFBSSxLQUFzRSxFQUFFLEVBRTNFO0FBRXdDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmljY29sby1wYXBlci1wb3J0Zm9saW8tY2xvbmUvLi9saWIvZ3NhcC50cz84OTkzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuaW1wb3J0IGdzYXAgZnJvbSAnZ3NhcCdcbmltcG9ydCB7IFNjcm9sbFRyaWdnZXIgfSBmcm9tICdnc2FwL1Njcm9sbFRyaWdnZXInXG5pbXBvcnQgeyBEcmFnZ2FibGUgfSBmcm9tICdnc2FwL0RyYWdnYWJsZSdcblxuaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmICFnc2FwLmNvcmUuZ2xvYmFscygpWydTY3JvbGxUcmlnZ2VyJ10pIHtcbiAgZ3NhcC5yZWdpc3RlclBsdWdpbihTY3JvbGxUcmlnZ2VyLCBEcmFnZ2FibGUpXG59XG5cbmV4cG9ydCB7IGdzYXAsIFNjcm9sbFRyaWdnZXIsIERyYWdnYWJsZSB9XG4iXSwibmFtZXMiOlsiZ3NhcCIsIlNjcm9sbFRyaWdnZXIiLCJEcmFnZ2FibGUiLCJjb3JlIiwiZ2xvYmFscyIsInJlZ2lzdGVyUGx1Z2luIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/gsap.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"da64997388ad\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uaWNjb2xvLXBhcGVyLXBvcnRmb2xpby1jbG9uZS8uL2FwcC9nbG9iYWxzLmNzcz8xZjAyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZGE2NDk5NzM4OGFkXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/fonts.ts":
/*!**********************!*\
  !*** ./app/fonts.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fraunces: () => (/* reexport default from dynamic */ next_font_google_target_css_path_app_fonts_ts_import_Fraunces_arguments_subsets_latin_variable_font_fraunces_weight_300_400_500_600_700_800_900_style_normal_italic_variableName_fraunces___WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   inter: () => (/* reexport default from dynamic */ next_font_google_target_css_path_app_fonts_ts_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_0___default.a),\n/* harmony export */   newsreader: () => (/* reexport default from dynamic */ next_font_google_target_css_path_app_fonts_ts_import_Newsreader_arguments_subsets_latin_variable_font_newsreader_weight_200_300_400_500_600_700_800_style_normal_italic_variableName_newsreader___WEBPACK_IMPORTED_MODULE_1___default.a)\n/* harmony export */ });\n/* harmony import */ var next_font_google_target_css_path_app_fonts_ts_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\fonts.ts\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\fonts.ts\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_fonts_ts_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_fonts_ts_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_fonts_ts_import_Newsreader_arguments_subsets_latin_variable_font_newsreader_weight_200_300_400_500_600_700_800_style_normal_italic_variableName_newsreader___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\fonts.ts\",\"import\":\"Newsreader\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-newsreader\",\"weight\":[\"200\",\"300\",\"400\",\"500\",\"600\",\"700\",\"800\"],\"style\":[\"normal\",\"italic\"]}],\"variableName\":\"newsreader\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\fonts.ts\\\",\\\"import\\\":\\\"Newsreader\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-newsreader\\\",\\\"weight\\\":[\\\"200\\\",\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\",\\\"800\\\"],\\\"style\\\":[\\\"normal\\\",\\\"italic\\\"]}],\\\"variableName\\\":\\\"newsreader\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_fonts_ts_import_Newsreader_arguments_subsets_latin_variable_font_newsreader_weight_200_300_400_500_600_700_800_style_normal_italic_variableName_newsreader___WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_fonts_ts_import_Newsreader_arguments_subsets_latin_variable_font_newsreader_weight_200_300_400_500_600_700_800_style_normal_italic_variableName_newsreader___WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_font_google_target_css_path_app_fonts_ts_import_Fraunces_arguments_subsets_latin_variable_font_fraunces_weight_300_400_500_600_700_800_900_style_normal_italic_variableName_fraunces___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\fonts.ts\",\"import\":\"Fraunces\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-fraunces\",\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"style\":[\"normal\",\"italic\"]}],\"variableName\":\"fraunces\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\fonts.ts\\\",\\\"import\\\":\\\"Fraunces\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-fraunces\\\",\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\",\\\"800\\\",\\\"900\\\"],\\\"style\\\":[\\\"normal\\\",\\\"italic\\\"]}],\\\"variableName\\\":\\\"fraunces\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_fonts_ts_import_Fraunces_arguments_subsets_latin_variable_font_fraunces_weight_300_400_500_600_700_800_900_style_normal_italic_variableName_fraunces___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_fonts_ts_import_Fraunces_arguments_subsets_latin_variable_font_fraunces_weight_300_400_500_600_700_800_900_style_normal_italic_variableName_fraunces___WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZm9udHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRWFBO0FBQ0FDO0FBT0FDO0FBUkFGO0FBQ0FDO0FBT0FDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmljY29sby1wYXBlci1wb3J0Zm9saW8tY2xvbmUvLi9hcHAvZm9udHMudHM/YjI1MCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBJbnRlciwgTmV3c3JlYWRlciwgLyogYWRkOiAqLyBGcmF1bmNlcyB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5cbmV4cG9ydCBjb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogWydsYXRpbiddLCB2YXJpYWJsZTogJy0tZm9udC1pbnRlcicgfSlcbmV4cG9ydCBjb25zdCBuZXdzcmVhZGVyID0gTmV3c3JlYWRlcih7XG4gIHN1YnNldHM6IFsnbGF0aW4nXSxcbiAgdmFyaWFibGU6ICctLWZvbnQtbmV3c3JlYWRlcicsXG4gIHdlaWdodDogWycyMDAnLCczMDAnLCc0MDAnLCc1MDAnLCc2MDAnLCc3MDAnLCc4MDAnXSxcbiAgc3R5bGU6IFsnbm9ybWFsJywnaXRhbGljJ10sXG59KVxuXG5leHBvcnQgY29uc3QgZnJhdW5jZXMgPSBGcmF1bmNlcyh7XG4gIHN1YnNldHM6IFsnbGF0aW4nXSxcbiAgdmFyaWFibGU6ICctLWZvbnQtZnJhdW5jZXMnLFxuICB3ZWlnaHQ6IFsnMzAwJywnNDAwJywnNTAwJywnNjAwJywnNzAwJywnODAwJywnOTAwJ10sXG4gIHN0eWxlOiBbJ25vcm1hbCcsJ2l0YWxpYyddLFxufSlcbiJdLCJuYW1lcyI6WyJpbnRlciIsIm5ld3NyZWFkZXIiLCJmcmF1bmNlcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/fonts.ts\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _fonts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./fonts */ \"(rsc)/./app/fonts.ts\");\n/* harmony import */ var _components_SmoothScroller__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/SmoothScroller */ \"(rsc)/./components/SmoothScroller.tsx\");\n// app/layout.tsx\n\n\n\n\nconst metadata = {\n    title: \"Paper Portfolio — GSAP/Next Replica\",\n    description: \"A tasteful homage to niccolomiranda.com built with Next.js + GSAP.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: `${_fonts__WEBPACK_IMPORTED_MODULE_2__.inter.variable} ${_fonts__WEBPACK_IMPORTED_MODULE_2__.newsreader.variable} ${_fonts__WEBPACK_IMPORTED_MODULE_2__.fraunces.variable}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"min-h-dvh selection:bg-accent/40 selection:text-ink\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SmoothScroller__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\app\\\\layout.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this),\n                children\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\app\\\\layout.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\app\\\\layout.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQUEsaUJBQWlCOztBQUNLO0FBRWdDO0FBQ0U7QUFFakQsTUFBTUksV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQUVDLFFBQVEsRUFBaUM7SUFDNUUscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7UUFBS0MsV0FBVyxDQUFDLEVBQUVYLHlDQUFLQSxDQUFDWSxRQUFRLENBQUMsQ0FBQyxFQUFFWCw4Q0FBVUEsQ0FBQ1csUUFBUSxDQUFDLENBQUMsRUFBRVYsNENBQVFBLENBQUNVLFFBQVEsQ0FBQyxDQUFDO2tCQUN4Riw0RUFBQ0M7WUFBS0YsV0FBVTs7OEJBQ2QsOERBQUNSLGtFQUFjQTs7Ozs7Z0JBQ2RLOzs7Ozs7Ozs7Ozs7QUFJVCIsInNvdXJjZXMiOlsid2VicGFjazovL25pY2NvbG8tcGFwZXItcG9ydGZvbGlvLWNsb25lLy4vYXBwL2xheW91dC50c3g/OTk4OCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBhcHAvbGF5b3V0LnRzeFxuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgeyBpbnRlciwgbmV3c3JlYWRlciwgZnJhdW5jZXMgIH0gZnJvbSAnLi9mb250cydcbmltcG9ydCBTbW9vdGhTY3JvbGxlciBmcm9tICdAL2NvbXBvbmVudHMvU21vb3RoU2Nyb2xsZXInXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnUGFwZXIgUG9ydGZvbGlvIOKAlCBHU0FQL05leHQgUmVwbGljYScsXG4gIGRlc2NyaXB0aW9uOiAnQSB0YXN0ZWZ1bCBob21hZ2UgdG8gbmljY29sb21pcmFuZGEuY29tIGJ1aWx0IHdpdGggTmV4dC5qcyArIEdTQVAuJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCIgY2xhc3NOYW1lPXtgJHtpbnRlci52YXJpYWJsZX0gJHtuZXdzcmVhZGVyLnZhcmlhYmxlfSAke2ZyYXVuY2VzLnZhcmlhYmxlfWB9PlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwibWluLWgtZHZoIHNlbGVjdGlvbjpiZy1hY2NlbnQvNDAgc2VsZWN0aW9uOnRleHQtaW5rXCI+XG4gICAgICAgIDxTbW9vdGhTY3JvbGxlciAvPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsiaW50ZXIiLCJuZXdzcmVhZGVyIiwiZnJhdW5jZXMiLCJTbW9vdGhTY3JvbGxlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiY2xhc3NOYW1lIiwidmFyaWFibGUiLCJib2R5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Page)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Header */ \"(rsc)/./components/Header.tsx\");\n/* harmony import */ var _components_Hero__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Hero */ \"(rsc)/./components/Hero.tsx\");\n/* harmony import */ var _components_FeaturedCarousel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/FeaturedCarousel */ \"(rsc)/./components/FeaturedCarousel.tsx\");\n/* harmony import */ var _components_ProcessSection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ProcessSection */ \"(rsc)/./components/ProcessSection.tsx\");\n/* harmony import */ var _components_Awards__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Awards */ \"(rsc)/./components/Awards.tsx\");\n/* harmony import */ var _components_Testimonials__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Testimonials */ \"(rsc)/./components/Testimonials.tsx\");\n/* harmony import */ var _components_CTA__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/CTA */ \"(rsc)/./components/CTA.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/Footer */ \"(rsc)/./components/Footer.tsx\");\n/* harmony import */ var _components_Cursor__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/Cursor */ \"(rsc)/./components/Cursor.tsx\");\n/* harmony import */ var _components_TopBar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/TopBar */ \"(rsc)/./components/TopBar.tsx\");\n/* harmony import */ var _components_Masthead__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/Masthead */ \"(rsc)/./components/Masthead.tsx\");\n/* harmony import */ var _components_WebsiteBanner__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/WebsiteBanner */ \"(rsc)/./components/WebsiteBanner.tsx\");\n/* harmony import */ var _components_PixelPerfect__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/PixelPerfect */ \"(rsc)/./components/PixelPerfect.tsx\");\n/* harmony import */ var _components_MarqueeStrip__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/MarqueeStrip */ \"(rsc)/./components/MarqueeStrip.tsx\");\n/* harmony import */ var _components_HeaderStrip__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/HeaderStrip */ \"(rsc)/./components/HeaderStrip.tsx\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Page() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TopBar__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\app\\\\page.tsx\",\n                lineNumber: 20,\n                columnNumber: 5\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\app\\\\page.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Cursor__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\app\\\\page.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HeaderStrip__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\app\\\\page.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Hero__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\app\\\\page.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Masthead__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\app\\\\page.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WebsiteBanner__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\app\\\\page.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FeaturedCarousel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\app\\\\page.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProcessSection__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\app\\\\page.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Awards__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\app\\\\page.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PixelPerfect__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\app\\\\page.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Testimonials__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\app\\\\page.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MarqueeStrip__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\app\\\\page.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CTA__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\app\\\\page.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\app\\\\page.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\app\\\\page.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Awards.tsx":
/*!*******************************!*\
  !*** ./components/Awards.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\coding projects\Next js\richport\components\Awards.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\coding projects\Next js\richport\components\Awards.tsx#default`));


/***/ }),

/***/ "(rsc)/./components/CTA.tsx":
/*!****************************!*\
  !*** ./components/CTA.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CTA)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction CTA() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"mx-auto max-w-6xl px-4 py-24 text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"font-serif text-5xl\",\n                children: \"Let's create something together\"\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\CTA.tsx\",\n                lineNumber: 4,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                href: \"mailto:<EMAIL>\",\n                className: \"mt-6 inline-block underline decoration-accent underline-offset-4 text-lg\",\n                children: \"Email Me\"\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\CTA.tsx\",\n                lineNumber: 5,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\CTA.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./components/CTA.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Cursor.tsx":
/*!*******************************!*\
  !*** ./components/Cursor.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\coding projects\Next js\richport\components\Cursor.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\coding projects\Next js\richport\components\Cursor.tsx#default`));


/***/ }),

/***/ "(rsc)/./components/FeaturedCarousel.tsx":
/*!*****************************************!*\
  !*** ./components/FeaturedCarousel.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\coding projects\Next js\richport\components\FeaturedCarousel.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\coding projects\Next js\richport\components\FeaturedCarousel.tsx#default`));


/***/ }),

/***/ "(rsc)/./components/Footer.tsx":
/*!*******************************!*\
  !*** ./components/Footer.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"border-t border-ink/10\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mx-auto max-w-6xl px-4 py-10 flex flex-col md:flex-row items-center justify-between gap-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm\",\n                    children: \"Miranda\\xa9 — 2021\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Footer.tsx\",\n                    lineNumber: 7,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex items-center gap-4 text-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"https://twitter.com\",\n                            className: \"underline underline-offset-4\",\n                            children: \"twitter\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Footer.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"\\xb7\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Footer.tsx\",\n                            lineNumber: 10,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"https://instagram.com\",\n                            className: \"underline underline-offset-4\",\n                            children: \"insta g ram\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Footer.tsx\",\n                            lineNumber: 11,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"\\xb7\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Footer.tsx\",\n                            lineNumber: 12,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"https://dribbble.com\",\n                            className: \"underline underline-offset-4\",\n                            children: \"dribbble\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Footer.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"\\xb7\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Footer.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"https://behance.net\",\n                            className: \"underline underline-offset-4\",\n                            children: \"behan c e\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Footer.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Footer.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Footer.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Footer.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\coding projects\Next js\richport\components\Header.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\coding projects\Next js\richport\components\Header.tsx#default`));


/***/ }),

/***/ "(rsc)/./components/HeaderStrip.tsx":
/*!************************************!*\
  !*** ./components/HeaderStrip.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\coding projects\Next js\richport\components\HeaderStrip.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\coding projects\Next js\richport\components\HeaderStrip.tsx#default`));


/***/ }),

/***/ "(rsc)/./components/Hero.tsx":
/*!*****************************!*\
  !*** ./components/Hero.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\coding projects\Next js\richport\components\Hero.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\coding projects\Next js\richport\components\Hero.tsx#default`));


/***/ }),

/***/ "(rsc)/./components/MarqueeStrip.tsx":
/*!*************************************!*\
  !*** ./components/MarqueeStrip.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MarqueeStrip)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/dist/api/image.js\");\n\n\nconst items = [\n    {\n        title: \"WOW Concept\",\n        src: \"https://picsum.photos/seed/m1/800/500\"\n    },\n    {\n        title: \"Unexpected Time\",\n        src: \"https://picsum.photos/seed/m2/800/500\"\n    },\n    {\n        title: \"The Books of Ye\",\n        src: \"https://picsum.photos/seed/m3/800/500\"\n    },\n    {\n        title: \"The Roger Hub\",\n        src: \"https://picsum.photos/seed/m4/800/500\"\n    }\n];\nfunction MarqueeStrip() {\n    const row = [\n        ...items,\n        ...items\n    ] // seamless loop\n    ;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"mx-auto max-w-6xl px-4 py-12\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"marquee boxed p-3\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"marquee-track\",\n                children: row.map((it, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"figure\", {\n                        className: \"flex items-center gap-3 shrink-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-44 h-24 rounded-md overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    src: it.src,\n                                    alt: it.title,\n                                    fill: true,\n                                    className: \"object-cover\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\MarqueeStrip.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\MarqueeStrip.tsx\",\n                                lineNumber: 18,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"figcaption\", {\n                                className: \"text-sm\",\n                                children: it.title\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\MarqueeStrip.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, i, true, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\MarqueeStrip.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\MarqueeStrip.tsx\",\n                lineNumber: 15,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\MarqueeStrip.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\MarqueeStrip.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/MarqueeStrip.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Masthead.tsx":
/*!*********************************!*\
  !*** ./components/Masthead.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\coding projects\Next js\richport\components\Masthead.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\coding projects\Next js\richport\components\Masthead.tsx#default`));


/***/ }),

/***/ "(rsc)/./components/PixelPerfect.tsx":
/*!*************************************!*\
  !*** ./components/PixelPerfect.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\coding projects\Next js\richport\components\PixelPerfect.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\coding projects\Next js\richport\components\PixelPerfect.tsx#default`));


/***/ }),

/***/ "(rsc)/./components/ProcessSection.tsx":
/*!***************************************!*\
  !*** ./components/ProcessSection.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\coding projects\Next js\richport\components\ProcessSection.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\coding projects\Next js\richport\components\ProcessSection.tsx#default`));


/***/ }),

/***/ "(rsc)/./components/SmoothScroller.tsx":
/*!***************************************!*\
  !*** ./components/SmoothScroller.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\coding projects\Next js\richport\components\SmoothScroller.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\coding projects\Next js\richport\components\SmoothScroller.tsx#default`));


/***/ }),

/***/ "(rsc)/./components/Testimonials.tsx":
/*!*************************************!*\
  !*** ./components/Testimonials.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\coding projects\Next js\richport\components\Testimonials.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\coding projects\Next js\richport\components\Testimonials.tsx#default`));


/***/ }),

/***/ "(rsc)/./components/TopBar.tsx":
/*!*******************************!*\
  !*** ./components/TopBar.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\coding projects\Next js\richport\components\TopBar.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\coding projects\Next js\richport\components\TopBar.tsx#default`));


/***/ }),

/***/ "(rsc)/./components/WebsiteBanner.tsx":
/*!**************************************!*\
  !*** ./components/WebsiteBanner.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\coding projects\Next js\richport\components\WebsiteBanner.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\coding projects\Next js\richport\components\WebsiteBanner.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/gsap","vendor-chunks/@swc","vendor-chunks/lenis","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5Ccoding%20projects%5CNext%20js%5Crichport%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Ccoding%20projects%5CNext%20js%5Crichport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();