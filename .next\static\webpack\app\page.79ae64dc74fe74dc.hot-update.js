"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_gsap__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/gsap */ \"(app-pages-browser)/./lib/gsap.ts\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst NavLink = (param)=>{\n    let { href, children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        href: href,\n        className: \"magnet group inline-flex items-center gap-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"tracking-loose2\",\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n                lineNumber: 10,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"block h-px w-6 bg-ink/40 transition-all group-hover:w-10\"\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n                lineNumber: 11,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined);\n};\n_c = NavLink;\nfunction Header() {\n    _s();\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // subtle reveal\n        _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.from(ref.current.querySelectorAll(\"[data-reveal] > *\"), {\n            opacity: 0,\n            y: 14,\n            stagger: 0.06,\n            duration: 0.5,\n            ease: \"power2.out\"\n        });\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-40 bg-paper supports-[backdrop-filter]:bg-paper/50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mx-auto max-w-6xl px-4 py-4 flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    href: \"/\",\n                    className: \"doodle-wrap flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm\",\n                            children: \"Kaitare, Richard\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"doodle-stroke w-10 h-8\",\n                            viewBox: \"0 0 100 80\",\n                            \"aria-hidden\": true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M10 40 Q 50 5, 90 40 Q 50 75, 10 40 z\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: (0,clsx__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(\"site-nav\", \"flex items-center gap-6 text-sm\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                            href: \"/\",\n                            children: \"Home\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                            href: \"/work\",\n                            children: \"Work\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                            href: \"/about\",\n                            children: \"About\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n_s(Header, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c1 = Header;\nvar _c, _c1;\n$RefreshReg$(_c, \"NavLink\");\n$RefreshReg$(_c1, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/Header.tsx\n"));

/***/ })

});