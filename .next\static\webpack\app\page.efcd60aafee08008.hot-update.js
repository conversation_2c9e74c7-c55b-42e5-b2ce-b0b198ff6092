/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CAwards.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CCursor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CFeaturedCarousel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CHeaderStrip.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CMasthead.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CPixelPerfect.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CProcessSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CTestimonials.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CTopBar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CWebsiteBanner.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=false!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CAwards.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CCursor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CFeaturedCarousel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CHeaderStrip.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CMasthead.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CPixelPerfect.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CProcessSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CTestimonials.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CTopBar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CWebsiteBanner.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=false! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Awards.tsx */ \"(app-pages-browser)/./components/Awards.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Cursor.tsx */ \"(app-pages-browser)/./components/Cursor.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/FeaturedCarousel.tsx */ \"(app-pages-browser)/./components/FeaturedCarousel.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Header.tsx */ \"(app-pages-browser)/./components/Header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/HeaderStrip.tsx */ \"(app-pages-browser)/./components/HeaderStrip.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Hero.tsx */ \"(app-pages-browser)/./components/Hero.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Masthead.tsx */ \"(app-pages-browser)/./components/Masthead.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/PixelPerfect.tsx */ \"(app-pages-browser)/./components/PixelPerfect.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ProcessSection.tsx */ \"(app-pages-browser)/./components/ProcessSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Testimonials.tsx */ \"(app-pages-browser)/./components/Testimonials.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/TopBar.tsx */ \"(app-pages-browser)/./components/TopBar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/WebsiteBanner.tsx */ \"(app-pages-browser)/./components/WebsiteBanner.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(app-pages-browser)/./node_modules/next/dist/client/image-component.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(app-pages-browser)/./node_modules/next/dist/client/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CAwards.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CCursor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CFeaturedCarousel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CHeaderStrip.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CMasthead.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CPixelPerfect.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CProcessSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CTestimonials.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CTopBar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Ccomponents%5C%5CWebsiteBanner.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccoding%20projects%5C%5CNext%20js%5C%5Crichport%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/TopBar.tsx":
/*!*******************************!*\
  !*** ./components/TopBar.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TopBar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_gsap__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/gsap */ \"(app-pages-browser)/./lib/gsap.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction TopBar() {\n    _s();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        _lib_gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.from(\".topbar [data-fade]\", {\n            y: -8,\n            opacity: 0,\n            stagger: 0.05,\n            duration: 0.45,\n            ease: \"power2.out\"\n        });\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"topbar border-b border-ink/15 bg-paper/70 backdrop-blur\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mx-auto max-w-6xl px-4 py-3 grid grid-cols-3 items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs md:text-[11px]\",\n                    \"data-fade\": true,\n                    children: \"Richard, Kaitare\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\TopBar.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"justify-self-center font-display text-[14px]\",\n                    \"data-fade\": true,\n                    children: \"The Paper Portfolio\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\TopBar.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"justify-self-end hamburger\",\n                    \"aria-label\": \"Open menu\",\n                    \"data-fade\": true,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\TopBar.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\TopBar.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 19\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\TopBar.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 27\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\TopBar.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\TopBar.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\TopBar.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n_s(TopBar, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = TopBar;\nvar _c;\n$RefreshReg$(_c, \"TopBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/TopBar.tsx\n"));

/***/ })

});