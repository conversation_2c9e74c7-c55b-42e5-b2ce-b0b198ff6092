"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/lenis";
exports.ids = ["vendor-chunks/lenis"];
exports.modules = {

/***/ "(ssr)/./node_modules/lenis/dist/lenis.mjs":
/*!*******************************************!*\
  !*** ./node_modules/lenis/dist/lenis.mjs ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Lenis)\n/* harmony export */ });\n// package.json\nvar version = \"1.3.11\";\n\n// packages/core/src/maths.ts\nfunction clamp(min, input, max) {\n  return Math.max(min, Math.min(input, max));\n}\nfunction lerp(x, y, t) {\n  return (1 - t) * x + t * y;\n}\nfunction damp(x, y, lambda, deltaTime) {\n  return lerp(x, y, 1 - Math.exp(-lambda * deltaTime));\n}\nfunction modulo(n, d) {\n  return (n % d + d) % d;\n}\n\n// packages/core/src/animate.ts\nvar Animate = class {\n  isRunning = false;\n  value = 0;\n  from = 0;\n  to = 0;\n  currentTime = 0;\n  // These are instanciated in the fromTo method\n  lerp;\n  duration;\n  easing;\n  onUpdate;\n  /**\n   * Advance the animation by the given delta time\n   *\n   * @param deltaTime - The time in seconds to advance the animation\n   */\n  advance(deltaTime) {\n    if (!this.isRunning) return;\n    let completed = false;\n    if (this.duration && this.easing) {\n      this.currentTime += deltaTime;\n      const linearProgress = clamp(0, this.currentTime / this.duration, 1);\n      completed = linearProgress >= 1;\n      const easedProgress = completed ? 1 : this.easing(linearProgress);\n      this.value = this.from + (this.to - this.from) * easedProgress;\n    } else if (this.lerp) {\n      this.value = damp(this.value, this.to, this.lerp * 60, deltaTime);\n      if (Math.round(this.value) === this.to) {\n        this.value = this.to;\n        completed = true;\n      }\n    } else {\n      this.value = this.to;\n      completed = true;\n    }\n    if (completed) {\n      this.stop();\n    }\n    this.onUpdate?.(this.value, completed);\n  }\n  /** Stop the animation */\n  stop() {\n    this.isRunning = false;\n  }\n  /**\n   * Set up the animation from a starting value to an ending value\n   * with optional parameters for lerping, duration, easing, and onUpdate callback\n   *\n   * @param from - The starting value\n   * @param to - The ending value\n   * @param options - Options for the animation\n   */\n  fromTo(from, to, { lerp: lerp2, duration, easing, onStart, onUpdate }) {\n    this.from = this.value = from;\n    this.to = to;\n    this.lerp = lerp2;\n    this.duration = duration;\n    this.easing = easing;\n    this.currentTime = 0;\n    this.isRunning = true;\n    onStart?.();\n    this.onUpdate = onUpdate;\n  }\n};\n\n// packages/core/src/debounce.ts\nfunction debounce(callback, delay) {\n  let timer;\n  return function(...args) {\n    let context = this;\n    clearTimeout(timer);\n    timer = setTimeout(() => {\n      timer = void 0;\n      callback.apply(context, args);\n    }, delay);\n  };\n}\n\n// packages/core/src/dimensions.ts\nvar Dimensions = class {\n  constructor(wrapper, content, { autoResize = true, debounce: debounceValue = 250 } = {}) {\n    this.wrapper = wrapper;\n    this.content = content;\n    if (autoResize) {\n      this.debouncedResize = debounce(this.resize, debounceValue);\n      if (this.wrapper instanceof Window) {\n        window.addEventListener(\"resize\", this.debouncedResize, false);\n      } else {\n        this.wrapperResizeObserver = new ResizeObserver(this.debouncedResize);\n        this.wrapperResizeObserver.observe(this.wrapper);\n      }\n      this.contentResizeObserver = new ResizeObserver(this.debouncedResize);\n      this.contentResizeObserver.observe(this.content);\n    }\n    this.resize();\n  }\n  width = 0;\n  height = 0;\n  scrollHeight = 0;\n  scrollWidth = 0;\n  // These are instanciated in the constructor as they need information from the options\n  debouncedResize;\n  wrapperResizeObserver;\n  contentResizeObserver;\n  destroy() {\n    this.wrapperResizeObserver?.disconnect();\n    this.contentResizeObserver?.disconnect();\n    if (this.wrapper === window && this.debouncedResize) {\n      window.removeEventListener(\"resize\", this.debouncedResize, false);\n    }\n  }\n  resize = () => {\n    this.onWrapperResize();\n    this.onContentResize();\n  };\n  onWrapperResize = () => {\n    if (this.wrapper instanceof Window) {\n      this.width = window.innerWidth;\n      this.height = window.innerHeight;\n    } else {\n      this.width = this.wrapper.clientWidth;\n      this.height = this.wrapper.clientHeight;\n    }\n  };\n  onContentResize = () => {\n    if (this.wrapper instanceof Window) {\n      this.scrollHeight = this.content.scrollHeight;\n      this.scrollWidth = this.content.scrollWidth;\n    } else {\n      this.scrollHeight = this.wrapper.scrollHeight;\n      this.scrollWidth = this.wrapper.scrollWidth;\n    }\n  };\n  get limit() {\n    return {\n      x: this.scrollWidth - this.width,\n      y: this.scrollHeight - this.height\n    };\n  }\n};\n\n// packages/core/src/emitter.ts\nvar Emitter = class {\n  events = {};\n  /**\n   * Emit an event with the given data\n   * @param event Event name\n   * @param args Data to pass to the event handlers\n   */\n  emit(event, ...args) {\n    let callbacks = this.events[event] || [];\n    for (let i = 0, length = callbacks.length; i < length; i++) {\n      callbacks[i]?.(...args);\n    }\n  }\n  /**\n   * Add a callback to the event\n   * @param event Event name\n   * @param cb Callback function\n   * @returns Unsubscribe function\n   */\n  on(event, cb) {\n    this.events[event]?.push(cb) || (this.events[event] = [cb]);\n    return () => {\n      this.events[event] = this.events[event]?.filter((i) => cb !== i);\n    };\n  }\n  /**\n   * Remove a callback from the event\n   * @param event Event name\n   * @param callback Callback function\n   */\n  off(event, callback) {\n    this.events[event] = this.events[event]?.filter((i) => callback !== i);\n  }\n  /**\n   * Remove all event listeners and clean up\n   */\n  destroy() {\n    this.events = {};\n  }\n};\n\n// packages/core/src/virtual-scroll.ts\nvar LINE_HEIGHT = 100 / 6;\nvar listenerOptions = { passive: false };\nvar VirtualScroll = class {\n  constructor(element, options = { wheelMultiplier: 1, touchMultiplier: 1 }) {\n    this.element = element;\n    this.options = options;\n    window.addEventListener(\"resize\", this.onWindowResize, false);\n    this.onWindowResize();\n    this.element.addEventListener(\"wheel\", this.onWheel, listenerOptions);\n    this.element.addEventListener(\n      \"touchstart\",\n      this.onTouchStart,\n      listenerOptions\n    );\n    this.element.addEventListener(\n      \"touchmove\",\n      this.onTouchMove,\n      listenerOptions\n    );\n    this.element.addEventListener(\"touchend\", this.onTouchEnd, listenerOptions);\n  }\n  touchStart = {\n    x: 0,\n    y: 0\n  };\n  lastDelta = {\n    x: 0,\n    y: 0\n  };\n  window = {\n    width: 0,\n    height: 0\n  };\n  emitter = new Emitter();\n  /**\n   * Add an event listener for the given event and callback\n   *\n   * @param event Event name\n   * @param callback Callback function\n   */\n  on(event, callback) {\n    return this.emitter.on(event, callback);\n  }\n  /** Remove all event listeners and clean up */\n  destroy() {\n    this.emitter.destroy();\n    window.removeEventListener(\"resize\", this.onWindowResize, false);\n    this.element.removeEventListener(\"wheel\", this.onWheel, listenerOptions);\n    this.element.removeEventListener(\n      \"touchstart\",\n      this.onTouchStart,\n      listenerOptions\n    );\n    this.element.removeEventListener(\n      \"touchmove\",\n      this.onTouchMove,\n      listenerOptions\n    );\n    this.element.removeEventListener(\n      \"touchend\",\n      this.onTouchEnd,\n      listenerOptions\n    );\n  }\n  /**\n   * Event handler for 'touchstart' event\n   *\n   * @param event Touch event\n   */\n  onTouchStart = (event) => {\n    const { clientX, clientY } = event.targetTouches ? event.targetTouches[0] : event;\n    this.touchStart.x = clientX;\n    this.touchStart.y = clientY;\n    this.lastDelta = {\n      x: 0,\n      y: 0\n    };\n    this.emitter.emit(\"scroll\", {\n      deltaX: 0,\n      deltaY: 0,\n      event\n    });\n  };\n  /** Event handler for 'touchmove' event */\n  onTouchMove = (event) => {\n    const { clientX, clientY } = event.targetTouches ? event.targetTouches[0] : event;\n    const deltaX = -(clientX - this.touchStart.x) * this.options.touchMultiplier;\n    const deltaY = -(clientY - this.touchStart.y) * this.options.touchMultiplier;\n    this.touchStart.x = clientX;\n    this.touchStart.y = clientY;\n    this.lastDelta = {\n      x: deltaX,\n      y: deltaY\n    };\n    this.emitter.emit(\"scroll\", {\n      deltaX,\n      deltaY,\n      event\n    });\n  };\n  onTouchEnd = (event) => {\n    this.emitter.emit(\"scroll\", {\n      deltaX: this.lastDelta.x,\n      deltaY: this.lastDelta.y,\n      event\n    });\n  };\n  /** Event handler for 'wheel' event */\n  onWheel = (event) => {\n    let { deltaX, deltaY, deltaMode } = event;\n    const multiplierX = deltaMode === 1 ? LINE_HEIGHT : deltaMode === 2 ? this.window.width : 1;\n    const multiplierY = deltaMode === 1 ? LINE_HEIGHT : deltaMode === 2 ? this.window.height : 1;\n    deltaX *= multiplierX;\n    deltaY *= multiplierY;\n    deltaX *= this.options.wheelMultiplier;\n    deltaY *= this.options.wheelMultiplier;\n    this.emitter.emit(\"scroll\", { deltaX, deltaY, event });\n  };\n  onWindowResize = () => {\n    this.window = {\n      width: window.innerWidth,\n      height: window.innerHeight\n    };\n  };\n};\n\n// packages/core/src/lenis.ts\nvar defaultEasing = (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t));\nvar Lenis = class {\n  _isScrolling = false;\n  // true when scroll is animating\n  _isStopped = false;\n  // true if user should not be able to scroll - enable/disable programmatically\n  _isLocked = false;\n  // same as isStopped but enabled/disabled when scroll reaches target\n  _preventNextNativeScrollEvent = false;\n  _resetVelocityTimeout = null;\n  __rafID = null;\n  /**\n   * Whether or not the user is touching the screen\n   */\n  isTouching;\n  /**\n   * The time in ms since the lenis instance was created\n   */\n  time = 0;\n  /**\n   * User data that will be forwarded through the scroll event\n   *\n   * @example\n   * lenis.scrollTo(100, {\n   *   userData: {\n   *     foo: 'bar'\n   *   }\n   * })\n   */\n  userData = {};\n  /**\n   * The last velocity of the scroll\n   */\n  lastVelocity = 0;\n  /**\n   * The current velocity of the scroll\n   */\n  velocity = 0;\n  /**\n   * The direction of the scroll\n   */\n  direction = 0;\n  /**\n   * The options passed to the lenis instance\n   */\n  options;\n  /**\n   * The target scroll value\n   */\n  targetScroll;\n  /**\n   * The animated scroll value\n   */\n  animatedScroll;\n  // These are instanciated here as they don't need information from the options\n  animate = new Animate();\n  emitter = new Emitter();\n  // These are instanciated in the constructor as they need information from the options\n  dimensions;\n  // This is not private because it's used in the Snap class\n  virtualScroll;\n  constructor({\n    wrapper = window,\n    content = document.documentElement,\n    eventsTarget = wrapper,\n    smoothWheel = true,\n    syncTouch = false,\n    syncTouchLerp = 0.075,\n    touchInertiaExponent = 1.7,\n    duration,\n    // in seconds\n    easing,\n    lerp: lerp2 = 0.1,\n    infinite = false,\n    orientation = \"vertical\",\n    // vertical, horizontal\n    gestureOrientation = orientation === \"horizontal\" ? \"both\" : \"vertical\",\n    // vertical, horizontal, both\n    touchMultiplier = 1,\n    wheelMultiplier = 1,\n    autoResize = true,\n    prevent,\n    virtualScroll,\n    overscroll = true,\n    autoRaf = false,\n    anchors = false,\n    autoToggle = false,\n    // https://caniuse.com/?search=transition-behavior\n    allowNestedScroll = false,\n    __experimental__naiveDimensions = false\n  } = {}) {\n    window.lenisVersion = version;\n    if (!wrapper || wrapper === document.documentElement) {\n      wrapper = window;\n    }\n    if (typeof duration === \"number\" && typeof easing !== \"function\") {\n      easing = defaultEasing;\n    } else if (typeof easing === \"function\" && typeof duration !== \"number\") {\n      duration = 1;\n    }\n    this.options = {\n      wrapper,\n      content,\n      eventsTarget,\n      smoothWheel,\n      syncTouch,\n      syncTouchLerp,\n      touchInertiaExponent,\n      duration,\n      easing,\n      lerp: lerp2,\n      infinite,\n      gestureOrientation,\n      orientation,\n      touchMultiplier,\n      wheelMultiplier,\n      autoResize,\n      prevent,\n      virtualScroll,\n      overscroll,\n      autoRaf,\n      anchors,\n      autoToggle,\n      allowNestedScroll,\n      __experimental__naiveDimensions\n    };\n    this.dimensions = new Dimensions(wrapper, content, { autoResize });\n    this.updateClassName();\n    this.targetScroll = this.animatedScroll = this.actualScroll;\n    this.options.wrapper.addEventListener(\"scroll\", this.onNativeScroll, false);\n    this.options.wrapper.addEventListener(\"scrollend\", this.onScrollEnd, {\n      capture: true\n    });\n    if (this.options.anchors && this.options.wrapper === window) {\n      this.options.wrapper.addEventListener(\n        \"click\",\n        this.onClick,\n        false\n      );\n    }\n    this.options.wrapper.addEventListener(\n      \"pointerdown\",\n      this.onPointerDown,\n      false\n    );\n    this.virtualScroll = new VirtualScroll(eventsTarget, {\n      touchMultiplier,\n      wheelMultiplier\n    });\n    this.virtualScroll.on(\"scroll\", this.onVirtualScroll);\n    if (this.options.autoToggle) {\n      this.rootElement.addEventListener(\"transitionend\", this.onTransitionEnd, {\n        passive: true\n      });\n    }\n    if (this.options.autoRaf) {\n      this.__rafID = requestAnimationFrame(this.raf);\n    }\n  }\n  /**\n   * Destroy the lenis instance, remove all event listeners and clean up the class name\n   */\n  destroy() {\n    this.emitter.destroy();\n    this.options.wrapper.removeEventListener(\n      \"scroll\",\n      this.onNativeScroll,\n      false\n    );\n    this.options.wrapper.removeEventListener(\"scrollend\", this.onScrollEnd, {\n      capture: true\n    });\n    this.options.wrapper.removeEventListener(\n      \"pointerdown\",\n      this.onPointerDown,\n      false\n    );\n    if (this.options.anchors && this.options.wrapper === window) {\n      this.options.wrapper.removeEventListener(\n        \"click\",\n        this.onClick,\n        false\n      );\n    }\n    this.virtualScroll.destroy();\n    this.dimensions.destroy();\n    this.cleanUpClassName();\n    if (this.__rafID) {\n      cancelAnimationFrame(this.__rafID);\n    }\n  }\n  on(event, callback) {\n    return this.emitter.on(event, callback);\n  }\n  off(event, callback) {\n    return this.emitter.off(event, callback);\n  }\n  onScrollEnd = (e) => {\n    if (!(e instanceof CustomEvent)) {\n      if (this.isScrolling === \"smooth\" || this.isScrolling === false) {\n        e.stopPropagation();\n      }\n    }\n  };\n  dispatchScrollendEvent = () => {\n    this.options.wrapper.dispatchEvent(\n      new CustomEvent(\"scrollend\", {\n        bubbles: this.options.wrapper === window,\n        // cancelable: false,\n        detail: {\n          lenisScrollEnd: true\n        }\n      })\n    );\n  };\n  onTransitionEnd = (event) => {\n    if (event.propertyName.includes(\"overflow\")) {\n      const property = this.isHorizontal ? \"overflow-x\" : \"overflow-y\";\n      const overflow = getComputedStyle(this.rootElement)[property];\n      if ([\"hidden\", \"clip\"].includes(overflow)) {\n        this.internalStop();\n      } else {\n        this.internalStart();\n      }\n    }\n  };\n  setScroll(scroll) {\n    if (this.isHorizontal) {\n      this.options.wrapper.scrollTo({ left: scroll, behavior: \"instant\" });\n    } else {\n      this.options.wrapper.scrollTo({ top: scroll, behavior: \"instant\" });\n    }\n  }\n  onClick = (event) => {\n    const path = event.composedPath();\n    const anchor = path.find(\n      (node) => node instanceof HTMLAnchorElement && (node.getAttribute(\"href\")?.startsWith(\"#\") || node.getAttribute(\"href\")?.startsWith(\"/#\") || node.getAttribute(\"href\")?.startsWith(\"./#\"))\n    );\n    if (anchor) {\n      const id = anchor.getAttribute(\"href\");\n      if (id) {\n        const options = typeof this.options.anchors === \"object\" && this.options.anchors ? this.options.anchors : void 0;\n        let target = `#${id.split(\"#\")[1]}`;\n        if ([\"#\", \"/#\", \"./#\", \"#top\", \"/#top\", \"./#top\"].includes(id)) {\n          target = 0;\n        }\n        this.scrollTo(target, options);\n      }\n    }\n  };\n  onPointerDown = (event) => {\n    if (event.button === 1) {\n      this.reset();\n    }\n  };\n  onVirtualScroll = (data) => {\n    if (typeof this.options.virtualScroll === \"function\" && this.options.virtualScroll(data) === false)\n      return;\n    const { deltaX, deltaY, event } = data;\n    this.emitter.emit(\"virtual-scroll\", { deltaX, deltaY, event });\n    if (event.ctrlKey) return;\n    if (event.lenisStopPropagation) return;\n    const isTouch = event.type.includes(\"touch\");\n    const isWheel = event.type.includes(\"wheel\");\n    this.isTouching = event.type === \"touchstart\" || event.type === \"touchmove\";\n    const isClickOrTap = deltaX === 0 && deltaY === 0;\n    const isTapToStop = this.options.syncTouch && isTouch && event.type === \"touchstart\" && isClickOrTap && !this.isStopped && !this.isLocked;\n    if (isTapToStop) {\n      this.reset();\n      return;\n    }\n    const isUnknownGesture = this.options.gestureOrientation === \"vertical\" && deltaY === 0 || this.options.gestureOrientation === \"horizontal\" && deltaX === 0;\n    if (isClickOrTap || isUnknownGesture) {\n      return;\n    }\n    let composedPath = event.composedPath();\n    composedPath = composedPath.slice(0, composedPath.indexOf(this.rootElement));\n    const prevent = this.options.prevent;\n    if (!!composedPath.find(\n      (node) => node instanceof HTMLElement && (typeof prevent === \"function\" && prevent?.(node) || node.hasAttribute?.(\"data-lenis-prevent\") || isTouch && node.hasAttribute?.(\"data-lenis-prevent-touch\") || isWheel && node.hasAttribute?.(\"data-lenis-prevent-wheel\") || this.options.allowNestedScroll && this.checkNestedScroll(node, { deltaX, deltaY }))\n    ))\n      return;\n    if (this.isStopped || this.isLocked) {\n      if (event.cancelable) {\n        event.preventDefault();\n      }\n      return;\n    }\n    const isSmooth = this.options.syncTouch && isTouch || this.options.smoothWheel && isWheel;\n    if (!isSmooth) {\n      this.isScrolling = \"native\";\n      this.animate.stop();\n      event.lenisStopPropagation = true;\n      return;\n    }\n    let delta = deltaY;\n    if (this.options.gestureOrientation === \"both\") {\n      delta = Math.abs(deltaY) > Math.abs(deltaX) ? deltaY : deltaX;\n    } else if (this.options.gestureOrientation === \"horizontal\") {\n      delta = deltaX;\n    }\n    if (!this.options.overscroll || this.options.infinite || this.options.wrapper !== window && this.limit > 0 && (this.animatedScroll > 0 && this.animatedScroll < this.limit || this.animatedScroll === 0 && deltaY > 0 || this.animatedScroll === this.limit && deltaY < 0)) {\n      event.lenisStopPropagation = true;\n    }\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n    const isSyncTouch = isTouch && this.options.syncTouch;\n    const isTouchEnd = isTouch && event.type === \"touchend\";\n    const hasTouchInertia = isTouchEnd;\n    if (hasTouchInertia) {\n      delta = Math.sign(this.velocity) * Math.pow(Math.abs(this.velocity), this.options.touchInertiaExponent);\n    }\n    this.scrollTo(this.targetScroll + delta, {\n      programmatic: false,\n      ...isSyncTouch ? {\n        lerp: hasTouchInertia ? this.options.syncTouchLerp : 1\n        // immediate: !hasTouchInertia,\n      } : {\n        lerp: this.options.lerp,\n        duration: this.options.duration,\n        easing: this.options.easing\n      }\n    });\n  };\n  /**\n   * Force lenis to recalculate the dimensions\n   */\n  resize() {\n    this.dimensions.resize();\n    this.animatedScroll = this.targetScroll = this.actualScroll;\n    this.emit();\n  }\n  emit() {\n    this.emitter.emit(\"scroll\", this);\n  }\n  onNativeScroll = () => {\n    if (this._resetVelocityTimeout !== null) {\n      clearTimeout(this._resetVelocityTimeout);\n      this._resetVelocityTimeout = null;\n    }\n    if (this._preventNextNativeScrollEvent) {\n      this._preventNextNativeScrollEvent = false;\n      return;\n    }\n    if (this.isScrolling === false || this.isScrolling === \"native\") {\n      const lastScroll = this.animatedScroll;\n      this.animatedScroll = this.targetScroll = this.actualScroll;\n      this.lastVelocity = this.velocity;\n      this.velocity = this.animatedScroll - lastScroll;\n      this.direction = Math.sign(\n        this.animatedScroll - lastScroll\n      );\n      if (!this.isStopped) {\n        this.isScrolling = \"native\";\n      }\n      this.emit();\n      if (this.velocity !== 0) {\n        this._resetVelocityTimeout = setTimeout(() => {\n          this.lastVelocity = this.velocity;\n          this.velocity = 0;\n          this.isScrolling = false;\n          this.emit();\n        }, 400);\n      }\n    }\n  };\n  reset() {\n    this.isLocked = false;\n    this.isScrolling = false;\n    this.animatedScroll = this.targetScroll = this.actualScroll;\n    this.lastVelocity = this.velocity = 0;\n    this.animate.stop();\n  }\n  /**\n   * Start lenis scroll after it has been stopped\n   */\n  start() {\n    if (!this.isStopped) return;\n    if (this.options.autoToggle) {\n      this.rootElement.style.removeProperty(\"overflow\");\n      return;\n    }\n    this.internalStart();\n  }\n  internalStart() {\n    if (!this.isStopped) return;\n    this.reset();\n    this.isStopped = false;\n    this.emit();\n  }\n  /**\n   * Stop lenis scroll\n   */\n  stop() {\n    if (this.isStopped) return;\n    if (this.options.autoToggle) {\n      this.rootElement.style.setProperty(\"overflow\", \"clip\");\n      return;\n    }\n    this.internalStop();\n  }\n  internalStop() {\n    if (this.isStopped) return;\n    this.reset();\n    this.isStopped = true;\n    this.emit();\n  }\n  /**\n   * RequestAnimationFrame for lenis\n   *\n   * @param time The time in ms from an external clock like `requestAnimationFrame` or Tempus\n   */\n  raf = (time) => {\n    const deltaTime = time - (this.time || time);\n    this.time = time;\n    this.animate.advance(deltaTime * 1e-3);\n    if (this.options.autoRaf) {\n      this.__rafID = requestAnimationFrame(this.raf);\n    }\n  };\n  /**\n   * Scroll to a target value\n   *\n   * @param target The target value to scroll to\n   * @param options The options for the scroll\n   *\n   * @example\n   * lenis.scrollTo(100, {\n   *   offset: 100,\n   *   duration: 1,\n   *   easing: (t) => 1 - Math.cos((t * Math.PI) / 2),\n   *   lerp: 0.1,\n   *   onStart: () => {\n   *     console.log('onStart')\n   *   },\n   *   onComplete: () => {\n   *     console.log('onComplete')\n   *   },\n   * })\n   */\n  scrollTo(target, {\n    offset = 0,\n    immediate = false,\n    lock = false,\n    duration = this.options.duration,\n    easing = this.options.easing,\n    lerp: lerp2 = this.options.lerp,\n    onStart,\n    onComplete,\n    force = false,\n    // scroll even if stopped\n    programmatic = true,\n    // called from outside of the class\n    userData\n  } = {}) {\n    if ((this.isStopped || this.isLocked) && !force) return;\n    if (typeof target === \"string\" && [\"top\", \"left\", \"start\"].includes(target)) {\n      target = 0;\n    } else if (typeof target === \"string\" && [\"bottom\", \"right\", \"end\"].includes(target)) {\n      target = this.limit;\n    } else {\n      let node;\n      if (typeof target === \"string\") {\n        node = document.querySelector(target);\n      } else if (target instanceof HTMLElement && target?.nodeType) {\n        node = target;\n      }\n      if (node) {\n        if (this.options.wrapper !== window) {\n          const wrapperRect = this.rootElement.getBoundingClientRect();\n          offset -= this.isHorizontal ? wrapperRect.left : wrapperRect.top;\n        }\n        const rect = node.getBoundingClientRect();\n        target = (this.isHorizontal ? rect.left : rect.top) + this.animatedScroll;\n      }\n    }\n    if (typeof target !== \"number\") return;\n    target += offset;\n    target = Math.round(target);\n    if (this.options.infinite) {\n      if (programmatic) {\n        this.targetScroll = this.animatedScroll = this.scroll;\n        const distance = target - this.animatedScroll;\n        if (distance > this.limit / 2) {\n          target = target - this.limit;\n        } else if (distance < -this.limit / 2) {\n          target = target + this.limit;\n        }\n      }\n    } else {\n      target = clamp(0, target, this.limit);\n    }\n    if (target === this.targetScroll) {\n      onStart?.(this);\n      onComplete?.(this);\n      return;\n    }\n    this.userData = userData ?? {};\n    if (immediate) {\n      this.animatedScroll = this.targetScroll = target;\n      this.setScroll(this.scroll);\n      this.reset();\n      this.preventNextNativeScrollEvent();\n      this.emit();\n      onComplete?.(this);\n      this.userData = {};\n      requestAnimationFrame(() => {\n        this.dispatchScrollendEvent();\n      });\n      return;\n    }\n    if (!programmatic) {\n      this.targetScroll = target;\n    }\n    if (typeof duration === \"number\" && typeof easing !== \"function\") {\n      easing = defaultEasing;\n    } else if (typeof easing === \"function\" && typeof duration !== \"number\") {\n      duration = 1;\n    }\n    this.animate.fromTo(this.animatedScroll, target, {\n      duration,\n      easing,\n      lerp: lerp2,\n      onStart: () => {\n        if (lock) this.isLocked = true;\n        this.isScrolling = \"smooth\";\n        onStart?.(this);\n      },\n      onUpdate: (value, completed) => {\n        this.isScrolling = \"smooth\";\n        this.lastVelocity = this.velocity;\n        this.velocity = value - this.animatedScroll;\n        this.direction = Math.sign(this.velocity);\n        this.animatedScroll = value;\n        this.setScroll(this.scroll);\n        if (programmatic) {\n          this.targetScroll = value;\n        }\n        if (!completed) this.emit();\n        if (completed) {\n          this.reset();\n          this.emit();\n          onComplete?.(this);\n          this.userData = {};\n          requestAnimationFrame(() => {\n            this.dispatchScrollendEvent();\n          });\n          this.preventNextNativeScrollEvent();\n        }\n      }\n    });\n  }\n  preventNextNativeScrollEvent() {\n    this._preventNextNativeScrollEvent = true;\n    requestAnimationFrame(() => {\n      this._preventNextNativeScrollEvent = false;\n    });\n  }\n  checkNestedScroll(node, { deltaX, deltaY }) {\n    const time = Date.now();\n    const cache = node._lenis ??= {};\n    let hasOverflowX, hasOverflowY, isScrollableX, isScrollableY, scrollWidth, scrollHeight, clientWidth, clientHeight;\n    const gestureOrientation = this.options.gestureOrientation;\n    if (time - (cache.time ?? 0) > 2e3) {\n      cache.time = Date.now();\n      const computedStyle = window.getComputedStyle(node);\n      cache.computedStyle = computedStyle;\n      const overflowXString = computedStyle.overflowX;\n      const overflowYString = computedStyle.overflowY;\n      hasOverflowX = [\"auto\", \"overlay\", \"scroll\"].includes(overflowXString);\n      hasOverflowY = [\"auto\", \"overlay\", \"scroll\"].includes(overflowYString);\n      cache.hasOverflowX = hasOverflowX;\n      cache.hasOverflowY = hasOverflowY;\n      if (!hasOverflowX && !hasOverflowY) return false;\n      if (gestureOrientation === \"vertical\" && !hasOverflowY) return false;\n      if (gestureOrientation === \"horizontal\" && !hasOverflowX) return false;\n      scrollWidth = node.scrollWidth;\n      scrollHeight = node.scrollHeight;\n      clientWidth = node.clientWidth;\n      clientHeight = node.clientHeight;\n      isScrollableX = scrollWidth > clientWidth;\n      isScrollableY = scrollHeight > clientHeight;\n      cache.isScrollableX = isScrollableX;\n      cache.isScrollableY = isScrollableY;\n      cache.scrollWidth = scrollWidth;\n      cache.scrollHeight = scrollHeight;\n      cache.clientWidth = clientWidth;\n      cache.clientHeight = clientHeight;\n    } else {\n      isScrollableX = cache.isScrollableX;\n      isScrollableY = cache.isScrollableY;\n      hasOverflowX = cache.hasOverflowX;\n      hasOverflowY = cache.hasOverflowY;\n      scrollWidth = cache.scrollWidth;\n      scrollHeight = cache.scrollHeight;\n      clientWidth = cache.clientWidth;\n      clientHeight = cache.clientHeight;\n    }\n    if (!hasOverflowX && !hasOverflowY || !isScrollableX && !isScrollableY) {\n      return false;\n    }\n    if (gestureOrientation === \"vertical\" && (!hasOverflowY || !isScrollableY))\n      return false;\n    if (gestureOrientation === \"horizontal\" && (!hasOverflowX || !isScrollableX))\n      return false;\n    let orientation;\n    if (gestureOrientation === \"horizontal\") {\n      orientation = \"x\";\n    } else if (gestureOrientation === \"vertical\") {\n      orientation = \"y\";\n    } else {\n      const isScrollingX = deltaX !== 0;\n      const isScrollingY = deltaY !== 0;\n      if (isScrollingX && hasOverflowX && isScrollableX) {\n        orientation = \"x\";\n      }\n      if (isScrollingY && hasOverflowY && isScrollableY) {\n        orientation = \"y\";\n      }\n    }\n    if (!orientation) return false;\n    let scroll, maxScroll, delta, hasOverflow, isScrollable;\n    if (orientation === \"x\") {\n      scroll = node.scrollLeft;\n      maxScroll = scrollWidth - clientWidth;\n      delta = deltaX;\n      hasOverflow = hasOverflowX;\n      isScrollable = isScrollableX;\n    } else if (orientation === \"y\") {\n      scroll = node.scrollTop;\n      maxScroll = scrollHeight - clientHeight;\n      delta = deltaY;\n      hasOverflow = hasOverflowY;\n      isScrollable = isScrollableY;\n    } else {\n      return false;\n    }\n    const willScroll = delta > 0 ? scroll < maxScroll : scroll > 0;\n    return willScroll && hasOverflow && isScrollable;\n  }\n  /**\n   * The root element on which lenis is instanced\n   */\n  get rootElement() {\n    return this.options.wrapper === window ? document.documentElement : this.options.wrapper;\n  }\n  /**\n   * The limit which is the maximum scroll value\n   */\n  get limit() {\n    if (this.options.__experimental__naiveDimensions) {\n      if (this.isHorizontal) {\n        return this.rootElement.scrollWidth - this.rootElement.clientWidth;\n      } else {\n        return this.rootElement.scrollHeight - this.rootElement.clientHeight;\n      }\n    } else {\n      return this.dimensions.limit[this.isHorizontal ? \"x\" : \"y\"];\n    }\n  }\n  /**\n   * Whether or not the scroll is horizontal\n   */\n  get isHorizontal() {\n    return this.options.orientation === \"horizontal\";\n  }\n  /**\n   * The actual scroll value\n   */\n  get actualScroll() {\n    const wrapper = this.options.wrapper;\n    return this.isHorizontal ? wrapper.scrollX ?? wrapper.scrollLeft : wrapper.scrollY ?? wrapper.scrollTop;\n  }\n  /**\n   * The current scroll value\n   */\n  get scroll() {\n    return this.options.infinite ? modulo(this.animatedScroll, this.limit) : this.animatedScroll;\n  }\n  /**\n   * The progress of the scroll relative to the limit\n   */\n  get progress() {\n    return this.limit === 0 ? 1 : this.scroll / this.limit;\n  }\n  /**\n   * Current scroll state\n   */\n  get isScrolling() {\n    return this._isScrolling;\n  }\n  set isScrolling(value) {\n    if (this._isScrolling !== value) {\n      this._isScrolling = value;\n      this.updateClassName();\n    }\n  }\n  /**\n   * Check if lenis is stopped\n   */\n  get isStopped() {\n    return this._isStopped;\n  }\n  set isStopped(value) {\n    if (this._isStopped !== value) {\n      this._isStopped = value;\n      this.updateClassName();\n    }\n  }\n  /**\n   * Check if lenis is locked\n   */\n  get isLocked() {\n    return this._isLocked;\n  }\n  set isLocked(value) {\n    if (this._isLocked !== value) {\n      this._isLocked = value;\n      this.updateClassName();\n    }\n  }\n  /**\n   * Check if lenis is smooth scrolling\n   */\n  get isSmooth() {\n    return this.isScrolling === \"smooth\";\n  }\n  /**\n   * The class name applied to the wrapper element\n   */\n  get className() {\n    let className = \"lenis\";\n    if (this.options.autoToggle) className += \" lenis-autoToggle\";\n    if (this.isStopped) className += \" lenis-stopped\";\n    if (this.isLocked) className += \" lenis-locked\";\n    if (this.isScrolling) className += \" lenis-scrolling\";\n    if (this.isScrolling === \"smooth\") className += \" lenis-smooth\";\n    return className;\n  }\n  updateClassName() {\n    this.cleanUpClassName();\n    this.rootElement.className = `${this.rootElement.className} ${this.className}`.trim();\n  }\n  cleanUpClassName() {\n    this.rootElement.className = this.rootElement.className.replace(/lenis(-\\w+)?/g, \"\").trim();\n  }\n};\n\n//# sourceMappingURL=lenis.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lenis/dist/lenis.mjs\n");

/***/ })

};
;