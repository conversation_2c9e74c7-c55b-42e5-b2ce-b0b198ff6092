
'use client'
import { useEffect } from 'react'
import { gsap } from '@/lib/gsap'

export default function TopBar() {
  useEffect(() => {
    gsap.from('.topbar [data-fade]', { y: -8, opacity: 0, stagger: 0.05, duration: 0.45, ease: 'power2.out' })
  }, [])

  return (
    <div className="topbar border-b border-ink/15 bg-paper">
      <div className="mx-auto max-w-6xl px-4 py-3 grid grid-cols-3 items-center">
        <div className="text-xs md:text-[11px]" data-fade><PERSON>, <PERSON><PERSON><PERSON></div>
        <div className="justify-self-center font-display text-[14px]" data-fade>The Paper Portfolio</div>
        <button
          className="justify-self-end hamburger"
          aria-label="Open menu"
          data-fade
        >
          <span /><span /><span />
        </button>
      </div>
    </div>
  )
}