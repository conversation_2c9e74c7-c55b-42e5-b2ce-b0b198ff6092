"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/HeaderStrip.tsx":
/*!************************************!*\
  !*** ./components/HeaderStrip.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HeaderStrip; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_gsap__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/gsap */ \"(app-pages-browser)/./lib/gsap.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction NewBadge() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"badge-new\",\n        children: \"NEW\"\n    }, void 0, false, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 7,\n        columnNumber: 10\n    }, this);\n}\n_c = NewBadge;\nfunction MiniEditorialCard(param) {\n    let { title, image, children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n        className: \"px-4 md:px-6 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative aspect-[16/9] rounded-md overflow-hidden mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    src: image,\n                    alt: title,\n                    fill: true,\n                    className: \"object-cover\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                className: \"font-display text-[22px] leading-none tracking-[.02em] uppercase flex items-center gap-2\",\n                children: [\n                    title,\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NewBadge, {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-[15px] leading-6 text-ink/80\",\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n_c1 = MiniEditorialCard;\nfunction CenterCopy() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-4 md:px-6 py-8 text-center md:text-left\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-display text-5xl md:text-[56px] leading-[0.95]\",\n                children: \"ALL WORK!\"\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-display text-[26px] md:text-[30px] leading-[1.05] mt-3\",\n                children: [\n                    \"A Featured selection\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 29\n                    }, this),\n                    \" the latest work —\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 52\n                    }, this),\n                    \" of the last years.\"\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 text-[12px] uppercase tracking-wider text-ink/70\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Tip!\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    \" Drag sideways to navigate\"\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n_c2 = CenterCopy;\nfunction HeaderStrip() {\n    _s();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!ref.current) return;\n        _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.from(ref.current.querySelectorAll(\"[data-reveal] > *\"), {\n            opacity: 0,\n            y: 14,\n            stagger: 0.06,\n            duration: 0.5,\n            ease: \"power2.out\"\n        });\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: ref,\n        className: \"border-y border-ink/15\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mx-auto max-w-6xl grid grid-cols-1 md:grid-cols-3 divide-y md:divide-y-0 md:divide-x divide-ink/20\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    \"data-reveal\": true,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MiniEditorialCard, {\n                        title: \"AvroKO\",\n                        image: \"/hero/thumbnail-small-3.jpeg\",\n                        children: \"AvroKO is an award-winning global design firm, established itself as a global leader in interior architecture for hospitality, restaurant and bars.\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-paper/60\",\n                    \"data-reveal\": true,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CenterCopy, {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    \"data-reveal\": true,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MiniEditorialCard, {\n                        title: \"The Roger Hub\",\n                        image: \"public/hero/thumbnail-small-3.jpeg\",\n                        children: \"The Roger Hub is an immersive web experience showcasing the tennis-inspired ‘On’ sneakers, a collaboration born out of a partnership with the legendary Roger Federer.\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n_s(HeaderStrip, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\n_c3 = HeaderStrip;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"NewBadge\");\n$RefreshReg$(_c1, \"MiniEditorialCard\");\n$RefreshReg$(_c2, \"CenterCopy\");\n$RefreshReg$(_c3, \"HeaderStrip\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/HeaderStrip.tsx\n"));

/***/ })

});